#!/bin/bash

# Android OTA签名更新工具启动脚本

echo "========================================"
echo "Android OTA签名更新工具"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.6或更高版本"
    exit 1
fi

# 检查ADB是否安装
if ! command -v adb &> /dev/null; then
    echo "错误: 未找到ADB工具，请先安装Android SDK Platform Tools"
    exit 1
fi

# 显示菜单
echo "1. 运行前置条件检查"
echo "2. 执行OTA签名更新"
echo "3. 退出"
echo
read -p "请选择操作 (1-3): " choice

case $choice in
    1)
        echo
        echo "正在运行前置条件检查..."
        python3 check_prerequisites.py
        ;;
    2)
        echo
        echo "正在执行OTA签名更新..."
        python3 update_ota_signature.py
        ;;
    3)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择，请重新运行脚本"
        exit 1
        ;;
esac
