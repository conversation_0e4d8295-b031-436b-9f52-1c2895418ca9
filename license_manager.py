#!/usr/bin/env python3
"""
一机一码授权管理模块
实现硬件信息获取、授权码生成和验证功能
"""

import hashlib
import hmac
import platform
import subprocess
import sys
import uuid
import json
import base64
from pathlib import Path
from typing import Dict, Optional, Tuple
from datetime import datetime, timedelta
import secrets

# RSA加密相关导入
try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.backends import default_backend
    RSA_AVAILABLE = True
except ImportError:
    RSA_AVAILABLE = False


class HardwareInfo:
    """硬件信息获取类"""
    
    @staticmethod
    def get_cpu_info() -> str:
        """获取CPU信息 - ACE优化：抑制console输出"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    ["wmic", "cpu", "get", "ProcessorId", "/value"],
                    capture_output=True, text=True, timeout=10,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                for line in result.stdout.split('\n'):
                    if 'ProcessorId=' in line:
                        return line.split('=')[1].strip()
            elif platform.system() == "Darwin":  # macOS
                result = subprocess.run(
                    ["system_profiler", "SPHardwareDataType"],
                    capture_output=True, text=True, timeout=10
                )
                for line in result.stdout.split('\n'):
                    if 'Serial Number' in line:
                        return line.split(':')[1].strip()
            elif platform.system() == "Linux":
                # 尝试从/proc/cpuinfo获取
                try:
                    with open('/proc/cpuinfo', 'r') as f:
                        for line in f:
                            if 'Serial' in line:
                                return line.split(':')[1].strip()
                except:
                    pass
                # 备用方案：使用dmidecode
                result = subprocess.run(
                    ["dmidecode", "-s", "processor-serial"],
                    capture_output=True, text=True, timeout=10
                )
                if result.returncode == 0:
                    return result.stdout.strip()
        except:
            pass
        return platform.processor()
    
    @staticmethod
    def get_motherboard_serial() -> str:
        """获取主板序列号 - ACE优化：抑制console输出"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    ["wmic", "baseboard", "get", "SerialNumber", "/value"],
                    capture_output=True, text=True, timeout=10,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        return line.split('=')[1].strip()
            elif platform.system() == "Darwin":  # macOS
                result = subprocess.run(
                    ["system_profiler", "SPHardwareDataType"],
                    capture_output=True, text=True, timeout=10
                )
                for line in result.stdout.split('\n'):
                    if 'Hardware UUID' in line:
                        return line.split(':')[1].strip()
            elif platform.system() == "Linux":
                result = subprocess.run(
                    ["dmidecode", "-s", "baseboard-serial-number"],
                    capture_output=True, text=True, timeout=10
                )
                if result.returncode == 0:
                    return result.stdout.strip()
        except:
            pass
        return str(uuid.getnode())
    
    @staticmethod
    def get_disk_serial() -> str:
        """获取硬盘序列号 - ACE优化：抑制console输出"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    ["wmic", "diskdrive", "get", "SerialNumber", "/value"],
                    capture_output=True, text=True, timeout=10,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line and line.split('=')[1].strip():
                        return line.split('=')[1].strip()
            elif platform.system() == "Darwin":  # macOS
                result = subprocess.run(
                    ["system_profiler", "SPSerialATADataType"],
                    capture_output=True, text=True, timeout=10
                )
                for line in result.stdout.split('\n'):
                    if 'Serial Number' in line:
                        return line.split(':')[1].strip()
            elif platform.system() == "Linux":
                # 尝试获取第一个硬盘的序列号
                result = subprocess.run(
                    ["lsblk", "-d", "-o", "SERIAL", "-n"],
                    capture_output=True, text=True, timeout=10
                )
                if result.returncode == 0:
                    serials = [s.strip() for s in result.stdout.split('\n') if s.strip()]
                    if serials:
                        return serials[0]
        except:
            pass
        return "UNKNOWN_DISK"
    
    @staticmethod
    def get_mac_address() -> str:
        """获取MAC地址"""
        try:
            mac = uuid.getnode()
            return ':'.join(['{:02x}'.format((mac >> elements) & 0xff) 
                           for elements in range(0, 2*6, 2)][::-1])
        except:
            return "UNKNOWN_MAC"
    
    @classmethod
    def get_hardware_fingerprint(cls) -> str:
        """获取硬件指纹 - ACE优化：确保稳定性"""
        # 获取基础硬件信息
        raw_info = {
            'cpu': cls.get_cpu_info(),
            'motherboard': cls.get_motherboard_serial(),
            'disk': cls.get_disk_serial(),
            'mac': cls.get_mac_address(),
            'platform': platform.platform(),
            'machine': platform.machine(),
        }

        # ACE优化：过滤和标准化硬件信息
        stable_info = {}

        # CPU信息处理
        cpu_info = raw_info['cpu'].strip()
        if cpu_info and cpu_info != "UNKNOWN_CPU":
            # 移除可能变化的部分（如频率、温度等）
            cpu_clean = ''.join(c for c in cpu_info if c.isalnum())
            stable_info['cpu'] = cpu_clean[:50]  # 限制长度
        else:
            # CPU信息不可用时，使用机器架构作为备用
            stable_info['cpu'] = platform.machine()

        # 主板信息处理
        mb_info = raw_info['motherboard'].strip()
        if mb_info and mb_info not in ["UNKNOWN_MB", ""]:
            stable_info['motherboard'] = mb_info
        else:
            # 主板信息不可用时，使用平台信息的稳定部分
            stable_info['motherboard'] = platform.system()

        # 硬盘信息处理
        disk_info = raw_info['disk'].strip()
        if disk_info and disk_info != "UNKNOWN_DISK":
            stable_info['disk'] = disk_info
        else:
            # 硬盘信息不可用时，使用节点ID
            stable_info['disk'] = str(uuid.getnode())

        # MAC地址处理
        mac_info = raw_info['mac'].strip()
        if mac_info and mac_info != "UNKNOWN_MAC":
            # 确保MAC地址格式一致
            mac_clean = mac_info.replace(':', '').replace('-', '').lower()
            stable_info['mac'] = mac_clean
        else:
            # MAC不可用时，使用节点ID
            stable_info['mac'] = str(uuid.getnode())

        # 平台信息处理 - 只使用稳定部分
        platform_info = raw_info['platform']
        # 移除可能包含版本号或构建信息的部分
        platform_parts = platform_info.split('-')
        if len(platform_parts) >= 2:
            # 使用系统名称和架构，忽略版本号
            stable_platform = f"{platform_parts[0]}-{platform_parts[-1]}"
        else:
            stable_platform = platform.system()
        stable_info['platform'] = stable_platform

        # 机器架构
        stable_info['machine'] = raw_info['machine']

        # 创建稳定的硬件指纹
        fingerprint_data = '|'.join([f"{k}:{v}" for k, v in sorted(stable_info.items())])
        fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()

        return fingerprint


class LicenseManager:
    """授权管理类 - ACE优化：RSA签名验证"""

    def __init__(self, secret_key: str = None):
        """初始化授权管理器"""
        self.secret_key = secret_key or "OTA_TOOL_SECRET_KEY_2024"  # 保留兼容性
        self.license_file = self._get_resource_path("license.dat")
        self.config_file = self._get_resource_path("license_config.json")

        # RSA公钥（内置在程序中，用于验签）
        self.rsa_public_key_pem = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuBKfdrEbR5atPp7wIvin
d0s+hIYzeUlHAIep63sTJ3GdMrEUnGI4BeggJgG8D9S+SRoZeIXsrEv5qz6feIuh
ILjRbJgOl6IF4nB5AI90vm3+/Ay35DjI9C149aKx9NMVP1T8LZEPBZj21A2fbJ9s
hgIZCdIxyjuUbQcHt5nUt6EcvpWd3A515IufOlvchxMZO5lTobt+lvVEt5pl7xww
XwXFK0+wB3HCoAR+E7zxvrU9oI/T0YiLMk2LMICkFBuQqfZUn/7kbAwAy1d47tjn
+zwu2tgKqYsh19qBMwCMtnWzV+FcCh2DeBYuMxzc+t9LNMbd0owtvqMJkxBlDrso
fwIDAQAB
-----END PUBLIC KEY-----"""

    def _get_resource_path(self, relative_path):
        """获取资源文件路径 - ACE优化：license.dat必须在exe同级目录"""
        try:
            # license.dat特殊处理：始终从exe同级目录读取
            if relative_path == "license.dat":
                # ACE修复：检测打包环境的多种方式
                is_packaged = (
                    getattr(sys, 'frozen', False) or  # 标准frozen检测
                    hasattr(sys, '_MEIPASS') or       # PyInstaller检测
                    'onefile' in str(sys.executable).lower() or  # Nuitka onefile检测
                    sys.executable.endswith('.exe') and not sys.executable.endswith('python.exe')  # exe文件检测
                )

                if is_packaged:
                    # 打包环境：exe同级目录（绝对路径）
                    exe_path = Path(sys.executable).resolve()
                    license_path = exe_path.parent / relative_path
                    return license_path
                else:
                    # 开发环境：脚本同级目录（绝对路径）
                    script_path = Path(__file__).resolve()
                    license_path = script_path.parent / relative_path
                    return license_path

            # 其他文件的常规处理（保持原有逻辑）
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller打包环境
                base_path = sys._MEIPASS
            elif hasattr(sys, 'frozen') and hasattr(sys, '_MEIPASS2'):
                # 其他打包环境
                base_path = sys._MEIPASS2
            elif getattr(sys, 'frozen', False):
                # Nuitka或其他打包环境
                base_path = Path(sys.executable).parent
            else:
                # 开发环境
                base_path = Path(__file__).parent

            resource_path = Path(base_path) / relative_path

            # 如果打包后的路径不存在，尝试exe同目录
            if not resource_path.exists() and getattr(sys, 'frozen', False):
                exe_dir = Path(sys.executable).parent
                resource_path = exe_dir / relative_path

            # 如果还是不存在，使用当前工作目录
            if not resource_path.exists():
                resource_path = Path.cwd() / relative_path

            return resource_path

        except Exception:
            # 出错时返回相对路径
            return Path(relative_path)

    def _load_rsa_public_key(self):
        """加载RSA公钥"""
        if not RSA_AVAILABLE:
            raise ImportError("cryptography库未安装，无法使用RSA功能")

        try:
            public_key = serialization.load_pem_public_key(
                self.rsa_public_key_pem.encode(),
                backend=default_backend()
            )
            return public_key
        except Exception as e:
            raise ValueError(f"加载RSA公钥失败: {str(e)}")

    def _verify_rsa_signature(self, message: str, signature: str) -> bool:
        """验证RSA签名"""
        if not RSA_AVAILABLE:
            return False

        try:
            # 加载公钥
            public_key = self._load_rsa_public_key()

            # 解码签名
            signature_bytes = base64.b64decode(signature)

            # 验证签名
            public_key.verify(
                signature_bytes,
                message.encode('utf-8'),
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except Exception:
            return False

    def generate_machine_code(self) -> str:
        """生成机器码"""
        fingerprint = HardwareInfo.get_hardware_fingerprint()
        # 使用前16位作为机器码，便于用户提供
        machine_code = fingerprint[:16].upper()
        return machine_code
    
    def generate_license_key(self, machine_code: str, days: int = 365) -> str:
        """生成授权码 - ACE注意：此方法需要RSA私钥，仅用于授权服务器"""
        # 计算过期时间
        expire_date = datetime.now() + timedelta(days=days)
        expire_timestamp = int(expire_date.timestamp())
        
        # 创建授权数据
        license_data = {
            'machine_code': machine_code,
            'expire_timestamp': expire_timestamp,
            'version': '1.0',
            'features': ['ota_update', 'partition_info', 'device_monitor']
        }
        
        # 序列化数据
        data_str = json.dumps(license_data, sort_keys=True)

        # ACE注意：此方法使用HMAC签名，生产环境应使用RSA私钥签名
        # RSA私钥不能打包进exe，需要在授权服务器上使用
        signature = hmac.new(
            self.secret_key.encode(),
            data_str.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # 组合数据和签名
        license_content = {
            'data': license_data,
            'signature': signature
        }
        
        # Base64编码
        license_key = base64.b64encode(
            json.dumps(license_content).encode()
        ).decode()
        
        return license_key
    
    def verify_license(self, license_key: str = None) -> Tuple[bool, str]:
        """验证授权 - ACE优化：RSA签名验证"""
        try:
            # 如果没有提供授权码，尝试从文件读取
            if not license_key:
                if not self.license_file.exists():
                    return False, "未找到授权文件"

                try:
                    with open(self.license_file, 'r', encoding='utf-8') as f:
                        license_key = f.read().strip()
                except Exception as read_error:
                    return False, f"读取授权文件失败: {str(read_error)}"
            
            # 解码授权码
            try:
                license_content = json.loads(
                    base64.b64decode(license_key.encode()).decode()
                )
            except:
                return False, "授权码格式错误"

            data = license_content.get('data', {})
            signature = license_content.get('signature', '')

            # ACE优化：使用RSA签名验证
            if RSA_AVAILABLE:
                # RSA签名验证
                data_str = json.dumps(data, sort_keys=True)
                if not self._verify_rsa_signature(data_str, signature):
                    return False, "RSA签名验证失败"
            else:
                # 回退到HMAC验证（兼容性）
                data_str = json.dumps(data, sort_keys=True)
                expected_signature = hmac.new(
                    self.secret_key.encode(),
                    data_str.encode(),
                    hashlib.sha256
                ).hexdigest()

                if not hmac.compare_digest(signature, expected_signature):
                    return False, "HMAC签名验证失败"
            
            # 验证机器码
            current_machine_code = self.generate_machine_code()
            if data.get('machine_code') != current_machine_code:
                return False, f"机器码不匹配\n当前机器码: {current_machine_code}"
            
            # 验证过期时间
            expire_timestamp = data.get('expire_timestamp', 0)
            if datetime.now().timestamp() > expire_timestamp:
                expire_date = datetime.fromtimestamp(expire_timestamp)
                return False, f"授权已过期\n过期时间: {expire_date.strftime('%Y-%m-%d %H:%M:%S')}"
            
            # 验证成功
            expire_date = datetime.fromtimestamp(expire_timestamp)
            return True, f"授权验证成功\n过期时间: {expire_date.strftime('%Y-%m-%d %H:%M:%S')}"
            
        except Exception as e:
            return False, f"授权验证异常: {str(e)}"
    
    def save_license(self, license_key: str) -> bool:
        """保存授权码到文件"""
        try:
            # 确保使用绝对路径
            license_path = self.license_file.resolve()

            # 确保目录存在
            license_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存授权文件
            with open(license_path, 'w', encoding='utf-8') as f:
                f.write(license_key)

            return True

        except Exception:
            return False
    
    def get_license_info(self) -> Dict:
        """获取授权信息"""
        machine_code = self.generate_machine_code()
        hardware_info = {
            'cpu': HardwareInfo.get_cpu_info(),
            'motherboard': HardwareInfo.get_motherboard_serial(),
            'disk': HardwareInfo.get_disk_serial(),
            'mac': HardwareInfo.get_mac_address(),
            'platform': platform.platform(),
        }
        
        license_status = "未授权"
        license_details = ""
        
        if self.license_file.exists():
            is_valid, message = self.verify_license()
            license_status = "已授权" if is_valid else "授权无效"
            license_details = message
        
        return {
            'machine_code': machine_code,
            'hardware_info': hardware_info,
            'license_status': license_status,
            'license_details': license_details
        }


# 测试函数
def test_license_system():
    """测试授权系统"""
    print("🔐 测试一机一码授权系统")
    print("=" * 50)
    
    lm = LicenseManager()
    
    # 获取机器码
    machine_code = lm.generate_machine_code()
    print(f"机器码: {machine_code}")
    
    # 生成授权码
    license_key = lm.generate_license_key(machine_code, days=30)
    print(f"授权码: {license_key[:50]}...")
    
    # 验证授权码
    is_valid, message = lm.verify_license(license_key)
    print(f"验证结果: {is_valid}")
    print(f"验证信息: {message}")
    
    # 获取授权信息
    info = lm.get_license_info()
    print("\n硬件信息:")
    for key, value in info['hardware_info'].items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    test_license_system()
