# 🔐 一机一码授权系统说明

## 📋 系统概述

本授权系统采用一机一码的安全机制，确保软件只能在授权的设备上运行。系统通过获取设备的硬件信息生成唯一的机器码，并与授权码进行绑定验证。

## 🔧 技术原理

### 硬件指纹生成
系统会获取以下硬件信息来生成唯一的硬件指纹：
- **CPU信息**: 处理器序列号或型号
- **主板序列号**: 主板的唯一标识
- **硬盘序列号**: 存储设备的序列号
- **MAC地址**: 网络接口的物理地址
- **平台信息**: 操作系统和架构信息

### 机器码生成
- 将所有硬件信息组合并使用SHA256哈希算法生成256位指纹
- 取前16位作为机器码，便于用户提供和管理
- 机器码格式：16位大写字母和数字组合

### 授权码生成
- 使用HMAC-SHA256算法对授权数据进行签名
- 包含机器码、过期时间、版本信息和功能列表
- 使用Base64编码便于传输和存储

## 📁 文件结构

```
├── license_manager.py      # 核心授权管理模块
├── license_dialog.py       # 授权验证对话框
├── license_generator.py    # 授权码生成工具
├── test_license_system.py  # 授权系统测试
├── ota_gui_pyside6.py     # 主程序（已集成授权）
└── license.dat            # 授权码存储文件（自动生成）
```

## 🚀 使用流程

### 1. 用户获取机器码
```bash
# 运行主程序，会自动显示授权对话框
python3 ota_gui_pyside6.py

# 或者运行测试程序查看机器码
python3 test_license_system.py
```

### 2. 软件提供商生成授权码
```bash
# 为单个机器码生成授权码
python3 license_generator.py --generate ABCD1234EFGH5678 --days 365

# 批量生成授权码
python3 license_generator.py --batch machine_codes.txt --days 365

# 交互式生成
python3 license_generator.py
```

### 3. 用户输入授权码
- 在授权对话框中输入获得的授权码
- 点击"保存授权码"按钮
- 点击"验证授权"确认授权有效性

### 4. 程序正常使用
- 授权验证成功后，程序正常启动
- 窗口标题显示机器码信息
- 菜单中可查看详细授权信息

## 🛠️ 授权码生成工具使用

### 命令行模式
```bash
# 生成单个授权码
python3 license_generator.py -g ABCD1234EFGH5678 -d 365

# 验证授权码
python3 license_generator.py -v "授权码内容"

# 批量生成（需要准备machine_codes.txt文件）
python3 license_generator.py -b machine_codes.txt -d 365
```

### 交互式模式
```bash
python3 license_generator.py
```
然后按照提示选择操作。

### 批量生成文件格式
创建`machine_codes.txt`文件，每行一个机器码：
```
ABCD1234EFGH5678
IJKL9012MNOP3456
QRST7890UVWX1234
```

## 🔍 授权验证流程

### 启动时验证
1. 程序启动时自动检查`license.dat`文件
2. 如果文件不存在或授权无效，显示授权对话框
3. 用户必须输入有效授权码才能继续使用

### 验证内容
- **签名验证**: 确保授权码未被篡改
- **机器码匹配**: 确保授权码与当前设备匹配
- **过期时间检查**: 确保授权码在有效期内
- **功能权限**: 验证授权的功能范围

## 🛡️ 安全特性

### 防篡改
- 使用HMAC-SHA256签名，防止授权码被修改
- 密钥硬编码在程序中，增加破解难度

### 硬件绑定
- 基于多种硬件信息生成指纹
- 即使部分硬件更换，仍能保持一定的稳定性

### 时间限制
- 支持设置授权有效期
- 过期后需要重新获取授权码

### 功能控制
- 可以控制不同授权码的功能权限
- 支持功能级别的授权管理

## 🧪 测试和调试

### 运行测试
```bash
# 完整测试授权系统
python3 test_license_system.py

# 测试硬件信息获取
python3 -c "from license_manager import HardwareInfo; print(HardwareInfo.get_hardware_fingerprint())"

# 测试授权对话框
python3 license_dialog.py
```

### 调试信息
- 测试程序会显示详细的硬件信息
- 可以验证机器码生成的一致性
- 测试各种异常情况的处理

## 📝 部署建议

### 开发环境
- 可以使用测试授权码进行开发
- 建议保留调试信息便于问题排查

### 生产环境
- 移除或注释调试输出
- 考虑对授权管理模块进行代码混淆
- 定期更新密钥和算法

### 用户支持
- 提供清晰的授权获取流程说明
- 准备常见问题解答
- 建立授权码管理和分发机制

## ⚠️ 注意事项

### 硬件变更
- 重大硬件更换可能导致机器码变化
- 建议提供硬件变更后的重新授权机制

### 备份恢复
- `license.dat`文件可以备份和恢复
- 建议用户保存授权码以备重装系统使用

### 兼容性
- 支持Windows、macOS、Linux系统
- 不同系统的硬件信息获取方式略有差异

## 🔄 升级和维护

### 版本兼容
- 授权码包含版本信息
- 支持向后兼容的版本升级

### 密钥轮换
- 定期更新签名密钥
- 提供密钥迁移机制

### 监控和统计
- 可以添加授权使用情况统计
- 监控异常的授权验证尝试

---

## 📞 技术支持

如有问题，请联系技术支持并提供：
- 机器码信息
- 错误信息截图
- 系统环境信息
- 授权码（如有）

**注意**: 请妥善保管授权码，避免泄露给未授权用户。
