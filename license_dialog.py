#!/usr/bin/env python3
"""
授权对话框界面
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QLineEdit, QGroupBox, QMessageBox, QApplication,
    QTabWidget, QWidget, QScrollArea, QFrame
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QClipboard, QIcon

from license_manager import LicenseManager


class LicenseDialog(QDialog):
    """授权对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.license_manager = LicenseManager()
        self.setup_ui()
        self.load_license_info()
        
    def get_application_icon(self):
        """获取应用程序图标 - ACE优化：与主程序一致的图标获取"""
        try:
            # 尝试多种图标文件和路径
            icon_candidates = [
                "icon.ico",      # 首选ICO格式
                "icon.png",      # 备选PNG格式
                Path(__file__).parent / "icon.ico",   # 脚本目录中的ICO
                Path(__file__).parent / "icon.png",   # 脚本目录中的PNG
            ]

            for icon_path in icon_candidates:
                try:
                    if Path(icon_path).exists():
                        icon = QIcon(str(icon_path))
                        if not icon.isNull():
                            return icon
                except Exception as e:
                    continue

            # 如果没有找到图标，返回空图标
            return QIcon()

        except Exception as e:
            return QIcon()

    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("软件授权验证")
        self.setFixedSize(700, 600)  # 增加对话框尺寸
        self.setModal(True)

        # ACE优化：设置对话框图标
        icon = self.get_application_icon()
        if not icon.isNull():
            self.setWindowIcon(icon)
        
        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 12px;
                background-color: white;
                min-height: 20px;
                font-size: 14px;
                line-height: 1.4;
            }
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 12px;
                background-color: white;
                min-height: 60px;
                font-size: 14px;
                line-height: 1.4;
            }
        """)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 标题
        title_label = QLabel("🔐 软件授权验证")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 授权验证选项卡
        license_tab = self.create_license_tab()
        tab_widget.addTab(license_tab, "授权验证")
        
        # 硬件信息选项卡
        hardware_tab = self.create_hardware_tab()
        tab_widget.addTab(hardware_tab, "硬件信息")
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.verify_btn = QPushButton("🔍 验证授权")
        self.verify_btn.clicked.connect(self.verify_license)
        button_layout.addWidget(self.verify_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("❌ 退出程序")
        self.close_btn.clicked.connect(self.reject)
        self.close_btn.setStyleSheet("background-color: #F44336;")
        button_layout.addWidget(self.close_btn)
        
        self.continue_btn = QPushButton("✅ 继续使用")
        self.continue_btn.clicked.connect(self.accept)
        self.continue_btn.setEnabled(False)
        self.continue_btn.setStyleSheet("background-color: #4CAF50;")
        button_layout.addWidget(self.continue_btn)
        
        layout.addLayout(button_layout)
        
    def create_license_tab(self):
        """创建授权验证选项卡 - ACE彻底重构版"""
        widget = QWidget()

        # ACE彻底重构：使用滚动区域确保所有内容可见
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(25)  # ACE彻底重构：更大的组间距
        layout.setContentsMargins(25, 25, 25, 25)  # ACE彻底重构：更大的边距

        # 机器码组 - ACE彻底重构：简化而稳定
        machine_group = QGroupBox("📱 机器码")
        machine_group.setMinimumHeight(180)  # ACE彻底重构：使用最小高度而非固定高度
        machine_layout = QVBoxLayout(machine_group)
        machine_layout.setSpacing(15)  # ACE彻底重构：更大间距
        machine_layout.setContentsMargins(25, 30, 25, 20)  # ACE彻底重构：更大内边距

        machine_code = self.license_manager.generate_machine_code()

        machine_info = QLabel("请将以下机器码发送给软件提供商以获取授权码：")
        machine_info.setStyleSheet("""
            color: #555;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
            padding: 5px 0px;
        """)
        machine_info.setMinimumHeight(50)  # ACE彻底重构：使用最小高度
        machine_layout.addWidget(machine_info)

        machine_code_layout = QHBoxLayout()
        machine_code_layout.setSpacing(15)  # ACE彻底重构：更大间距
        machine_code_layout.setContentsMargins(0, 0, 0, 0)

        self.machine_code_edit = QLineEdit(machine_code)
        self.machine_code_edit.setReadOnly(True)
        self.machine_code_edit.setStyleSheet("""
            QLineEdit {
                font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
                font-size: 16px;
                font-weight: bold;
                padding: 15px 18px;
                background-color: #f8f9fa;
                border: 2px solid #007bff;
                border-radius: 10px;
                color: #0056b3;
                selection-background-color: #007bff;
                selection-color: white;
            }
        """)
        self.machine_code_edit.setMinimumHeight(50)  # ACE彻底重构：使用最小高度
        machine_code_layout.addWidget(self.machine_code_edit)

        copy_machine_btn = QPushButton("复制")
        copy_machine_btn.setToolTip("复制机器码到剪切板")
        copy_machine_btn.setFixedSize(70, 50)  # ACE彻底重构：更大按钮
        copy_machine_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 14px;
                font-weight: bold;
                padding: 0px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        copy_machine_btn.clicked.connect(self.copy_machine_code)
        machine_code_layout.addWidget(copy_machine_btn)

        machine_layout.addLayout(machine_code_layout)
        layout.addWidget(machine_group)

        # 授权码输入组 - ACE彻底重构：完全独立的区域
        license_group = QGroupBox("🔑 授权码")
        license_group.setMinimumHeight(350)  # ACE彻底重构：更大的最小高度
        license_layout = QVBoxLayout(license_group)
        license_layout.setSpacing(15)  # ACE彻底重构：更大间距
        license_layout.setContentsMargins(25, 30, 25, 20)  # ACE彻底重构：更大内边距

        # 说明文字
        license_info = QLabel("请输入从软件提供商处获得的授权码：")
        license_info.setStyleSheet("""
            QLabel {
                color: #555;
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 10px;
                padding: 5px 0px;
                background-color: transparent;
            }
        """)
        license_info.setMinimumHeight(50)  # ACE彻底重构：使用最小高度
        license_layout.addWidget(license_info)

        # 授权码输入框 - ACE彻底重构：更大高度
        self.license_input = QTextEdit()
        self.license_input.setMinimumHeight(120)  # ACE彻底重构：更大的最小高度
        self.license_input.setPlaceholderText("请粘贴授权码...")
        self.license_input.setStyleSheet("""
            QTextEdit {
                font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
                font-size: 12px;
                line-height: 1.5;
                padding: 15px 18px;
                border: 2px solid #ddd;
                border-radius: 10px;
                background-color: white;
                selection-background-color: #007bff;
                selection-color: white;
                color: #333;
            }
            QTextEdit:focus {
                border-color: #007bff;
                outline: none;
                background-color: #fafbfc;
            }
            QTextEdit:hover {
                border-color: #80bdff;
            }
        """)
        license_layout.addWidget(self.license_input)

        # 连接文本变化信号
        self.license_input.textChanged.connect(self.update_save_button_state)

        # 按钮区域 - 优化间距：调整清空按钮与输入框的距离
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 25, 0, 0)  # 增加顶部间距：20px → 25px
        button_layout.setSpacing(30)  # 增加按钮间距：15px → 18px

        # 清空按钮
        self.clear_license_btn = QPushButton("清空")
        self.clear_license_btn.setFixedSize(80, 40)  # ACE彻底重构：更大尺寸
        self.clear_license_btn.clicked.connect(self.clear_license_input)
        self.clear_license_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #495057;
            }
        """)
        button_layout.addWidget(self.clear_license_btn)

        # 保存按钮
        self.save_license_btn = QPushButton("保存授权码")
        self.save_license_btn.setFixedSize(120, 40)  # ACE彻底重构：更大尺寸
        self.save_license_btn.clicked.connect(self.save_license)
        self.save_license_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)
        button_layout.addWidget(self.save_license_btn)

        # 添加弹性空间，使按钮左对齐
        button_layout.addStretch()

        # 将按钮布局直接添加到授权码组
        license_layout.addLayout(button_layout)

        # 初始化保存按钮状态
        self.update_save_button_state()

        layout.addWidget(license_group)
        
        # 授权状态组 - ACE彻底重构：完全独立的状态显示区域
        status_group = QGroupBox("📊 授权状态")
        status_group.setMinimumHeight(200)  # ACE彻底重构：更大的最小高度
        status_layout = QVBoxLayout(status_group)
        status_layout.setSpacing(15)  # ACE彻底重构：更大间距
        status_layout.setContentsMargins(25, 30, 25, 20)  # ACE彻底重构：与其他组一致

        self.status_label = QLabel("未授权")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #dc3545;
                padding: 15px 25px;
                background-color: rgba(220, 53, 69, 0.1);
                border-radius: 10px;
                border: 1px solid rgba(220, 53, 69, 0.2);
            }
        """)
        self.status_label.setMinimumHeight(55)  # ACE彻底重构：更大的最小高度
        self.status_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        status_layout.addWidget(self.status_label)

        self.status_details = QLabel("")
        self.status_details.setStyleSheet("""
            QLabel {
                color: #666;
                padding: 15px 18px;
                background-color: #f8f9fa;
                border-radius: 10px;
                font-size: 13px;
                line-height: 1.5;
                border: 1px solid #e9ecef;
            }
        """)
        self.status_details.setWordWrap(True)
        self.status_details.setMinimumHeight(60)  # ACE彻底重构：更大的最小高度
        self.status_details.setAlignment(Qt.AlignTop)  # 顶部对齐
        status_layout.addWidget(self.status_details)

        layout.addWidget(status_group)

        # ACE彻底重构：设置滚动区域
        scroll_area.setWidget(content_widget)

        # 主布局
        main_layout = QVBoxLayout(widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)
        
        layout.addStretch()
        return widget
        
    def create_hardware_tab(self):
        """创建硬件信息选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 硬件信息组
        hardware_group = QGroupBox("🖥️ 硬件信息")
        hardware_layout = QVBoxLayout(hardware_group)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        self.hardware_info_label = QLabel("正在获取硬件信息...")
        self.hardware_info_label.setStyleSheet("""
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.6;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            color: #333;
        """)
        self.hardware_info_label.setWordWrap(True)
        self.hardware_info_label.setMinimumHeight(200)  # 设置最小高度
        scroll_layout.addWidget(self.hardware_info_label)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        hardware_layout.addWidget(scroll_area)
        
        layout.addWidget(hardware_group)
        
        return widget
        
    def load_license_info(self):
        """加载授权信息"""
        # 获取授权信息
        info = self.license_manager.get_license_info()
        
        # 更新硬件信息显示
        hardware_text = "硬件信息详情:\n\n"
        for key, value in info['hardware_info'].items():
            hardware_text += f"{key.upper()}: {value}\n"
        
        hardware_text += f"\n机器码: {info['machine_code']}"
        hardware_text += f"\n授权状态: {info['license_status']}"
        
        if info['license_details']:
            hardware_text += f"\n详细信息: {info['license_details']}"
            
        self.hardware_info_label.setText(hardware_text)
        
        # 更新授权状态
        self.update_license_status(info['license_status'], info['license_details'])
        
        # 如果已经有有效授权，自动验证
        if info['license_status'] == "已授权":
            self.verify_license()
            
    def update_license_status(self, status: str, details: str):
        """更新授权状态显示"""
        self.status_label.setText(status)
        self.status_details.setText(details)

        if status == "已授权":
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 15px;
                    font-weight: bold;
                    color: #4CAF50;
                    padding: 10px 15px;
                    background-color: rgba(76, 175, 80, 0.1);
                    border-radius: 6px;
                    border: 1px solid rgba(76, 175, 80, 0.2);
                }
            """)
            self.continue_btn.setEnabled(True)
        else:
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 15px;
                    font-weight: bold;
                    color: #F44336;
                    padding: 10px 15px;
                    background-color: rgba(244, 67, 54, 0.1);
                    border-radius: 6px;
                    border: 1px solid rgba(244, 67, 54, 0.2);
                }
            """)
            self.continue_btn.setEnabled(False)
            
    def copy_machine_code(self):
        """复制机器码"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.machine_code_edit.text())
        QMessageBox.information(self, "成功", "机器码已复制到剪切板")

    def clear_license_input(self):
        """清空授权码输入框"""
        self.license_input.clear()
        self.license_input.setPlaceholderText("请粘贴授权码...")
        # 重置保存按钮状态
        self.update_save_button_state()

    def update_save_button_state(self):
        """更新保存按钮状态"""
        has_content = bool(self.license_input.toPlainText().strip())

        # 直接使用按钮引用更新状态
        if hasattr(self, 'save_license_btn'):
            self.save_license_btn.setEnabled(has_content)
            if has_content:
                self.save_license_btn.setToolTip("点击保存授权码")
            else:
                self.save_license_btn.setToolTip("请先输入授权码")
        
    def save_license(self):
        """保存授权码"""
        license_key = self.license_input.toPlainText().strip()
        if not license_key:
            QMessageBox.warning(self, "警告", "请输入授权码")
            return
            
        if self.license_manager.save_license(license_key):
            QMessageBox.information(self, "成功", "授权码已保存")
            self.verify_license()
        else:
            QMessageBox.critical(self, "错误", "授权码保存失败")
            
    def verify_license(self):
        """验证授权"""
        is_valid, message = self.license_manager.verify_license()
        
        if is_valid:
            self.update_license_status("已授权", message)
            QMessageBox.information(self, "授权验证", f"✅ {message}")
        else:
            self.update_license_status("授权无效", message)
            QMessageBox.warning(self, "授权验证", f"❌ {message}")


def show_license_dialog():
    """显示授权对话框"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    dialog = LicenseDialog()
    result = dialog.exec()
    
    return result == QDialog.Accepted


if __name__ == "__main__":
    # 测试授权对话框
    app = QApplication(sys.argv)
    dialog = LicenseDialog()
    dialog.show()
    sys.exit(app.exec())
