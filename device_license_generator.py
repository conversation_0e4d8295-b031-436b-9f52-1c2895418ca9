#!/usr/bin/env python3
"""
设备端授权生成器 - ACE实现
基于设备序列号生成RSA签名的授权文件
授权文件格式：序列号.dat
"""

import json
import base64
from datetime import datetime, timedelta
from pathlib import Path

try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.backends import default_backend
    RSA_AVAILABLE = True
except ImportError:
    print("❌ 错误：需要安装cryptography库")
    print("请运行：pip install cryptography")
    RSA_AVAILABLE = False
    exit(1)


class DeviceLicenseGenerator:
    """设备授权生成器 - ACE实现：基于序列号的RSA签名"""
    
    def __init__(self):
        self.private_key_file = Path("rsa_private_key.pem")
        self.public_key_file = Path("rsa_public_key.pem")
        self.licenses_dir = Path("licenses")
        
        # 确保licenses目录存在
        self.licenses_dir.mkdir(exist_ok=True)
    
    def load_private_key(self):
        """加载RSA私钥"""
        if not self.private_key_file.exists():
            raise FileNotFoundError(f"RSA私钥文件不存在: {self.private_key_file}")
        
        with open(self.private_key_file, 'rb') as f:
            private_key = serialization.load_pem_private_key(
                f.read(),
                password=None,
                backend=default_backend()
            )
        return private_key
    
    def sign_device_data(self, data: str) -> str:
        """使用RSA私钥签名设备数据"""
        private_key = self.load_private_key()
        
        signature = private_key.sign(
            data.encode('utf-8'),
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        return base64.b64encode(signature).decode()
    
    def generate_device_license(self, serial_number: str, device_name: str = "", days: int = 365) -> str:
        """生成设备RSA签名授权码"""
        # 清理序列号
        clean_serial = serial_number.strip().upper()
        
        if not clean_serial:
            raise ValueError("设备序列号不能为空")
        
        # 计算过期时间
        expire_date = datetime.now() + timedelta(days=days)
        expire_timestamp = int(expire_date.timestamp())
        
        # 创建设备授权数据
        device_data = {
            'serial_number': clean_serial,
            'device_name': device_name or f"Device_{clean_serial}",
            'expire_timestamp': expire_timestamp,
            'issue_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'license_type': 'device_authorization',
            'version': '1.0'
        }
        
        # 序列化数据
        data_str = json.dumps(device_data, sort_keys=True)
        
        # 使用RSA私钥签名
        signature = self.sign_device_data(data_str)
        
        # 组合数据和签名
        license_content = {
            'data': device_data,
            'signature': signature
        }
        
        # Base64编码
        license_key = base64.b64encode(
            json.dumps(license_content).encode()
        ).decode()
        
        return license_key
    
    def save_device_license(self, serial_number: str, device_name: str = "", days: int = 365) -> str:
        """生成并保存设备授权文件"""
        # 生成授权码
        license_key = self.generate_device_license(serial_number, device_name, days)
        
        # 清理序列号用作文件名
        clean_serial = serial_number.strip().upper()
        
        # 保存到文件
        license_file = self.licenses_dir / f"{clean_serial}.dat"
        
        with open(license_file, 'w', encoding='utf-8') as f:
            f.write(license_key)
        
        return str(license_file)
    
    def batch_generate_licenses(self, device_list: list, days: int = 365):
        """批量生成设备授权"""
        results = []
        
        for device_info in device_list:
            if isinstance(device_info, str):
                serial = device_info
                name = ""
            elif isinstance(device_info, dict):
                serial = device_info.get('serial', '')
                name = device_info.get('name', '')
            else:
                continue
            
            try:
                license_file = self.save_device_license(serial, name, days)
                results.append({
                    'serial': serial,
                    'name': name,
                    'status': 'success',
                    'file': license_file
                })
            except Exception as e:
                results.append({
                    'serial': serial,
                    'name': name,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return results


def main():
    """主函数 - 设备授权生成工具"""
    print("🔐 设备端RSA授权生成器")
    print("=" * 50)
    
    if not RSA_AVAILABLE:
        return
    
    generator = DeviceLicenseGenerator()
    
    # 检查RSA密钥
    if not generator.private_key_file.exists():
        print("❌ 错误：RSA私钥文件不存在")
        print("请先运行 license_generator_rsa.py 生成RSA密钥对")
        return
    
    while True:
        print("\n📱 设备授权生成")
        print("-" * 30)
        
        # 选择模式
        print("请选择操作模式:")
        print("1. 单个设备授权")
        print("2. 批量设备授权")
        print("3. 退出")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == "1":
            # 单个设备授权
            serial = input("请输入设备序列号: ").strip()
            if not serial:
                print("❌ 设备序列号不能为空")
                continue
            
            name = input("请输入设备名称 (可选): ").strip()
            
            try:
                days = int(input("请输入有效期（天数，默认365）: ") or "365")
            except ValueError:
                print("❌ 有效期必须是数字")
                continue
            
            try:
                license_file = generator.save_device_license(serial, name, days)
                
                # 计算过期时间
                expire_date = datetime.now() + timedelta(days=days)
                
                print()
                print("✅ 设备授权生成成功!")
                print(f"设备序列号: {serial}")
                print(f"设备名称: {name or f'Device_{serial}'}")
                print(f"有效期: {days} 天")
                print(f"过期时间: {expire_date.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"授权文件: {license_file}")
                
            except Exception as e:
                print(f"❌ 生成设备授权失败: {str(e)}")
        
        elif choice == "2":
            # 批量设备授权
            print("\n批量设备授权模式")
            print("请输入设备信息，格式：序列号,设备名称")
            print("每行一个设备，设备名称可选")
            print("输入空行结束输入")
            
            device_list = []
            while True:
                line = input("设备信息: ").strip()
                if not line:
                    break
                
                parts = line.split(',', 1)
                serial = parts[0].strip()
                name = parts[1].strip() if len(parts) > 1 else ""
                
                if serial:
                    device_list.append({'serial': serial, 'name': name})
            
            if not device_list:
                print("❌ 没有输入任何设备信息")
                continue
            
            try:
                days = int(input("请输入有效期（天数，默认365）: ") or "365")
            except ValueError:
                print("❌ 有效期必须是数字")
                continue
            
            print(f"\n开始批量生成 {len(device_list)} 个设备的授权...")
            
            results = generator.batch_generate_licenses(device_list, days)
            
            print("\n批量生成结果:")
            print("-" * 50)
            
            success_count = 0
            for result in results:
                if result['status'] == 'success':
                    success_count += 1
                    print(f"✅ {result['serial']} ({result['name']}) - {result['file']}")
                else:
                    print(f"❌ {result['serial']} ({result['name']}) - {result['error']}")
            
            print(f"\n批量生成完成: {success_count}/{len(device_list)} 成功")
        
        elif choice == "3":
            break
        
        else:
            print("❌ 无效选择，请重新输入")
    
    print("👋 再见!")


if __name__ == "__main__":
    main()
