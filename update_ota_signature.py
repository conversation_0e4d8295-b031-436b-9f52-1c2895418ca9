#!/usr/bin/env python3
"""
Android OTA签名自动更新脚本
自动化执行OTA签名更新流程，包括禁用verity、推送镜像文件、更新签名证书等操作
"""

import subprocess
import sys
import time
import os
from pathlib import Path


class ADBManager:
    """ADB命令管理器"""
    
    def __init__(self, files_dir="files"):
        self.files_dir = Path(files_dir)
        # ACE优化：vbmeta.img改为可选文件，支持不存在vbmeta分区的设备
        self.required_files = [
            "boot.img",
            "update-payload-key.pub.pem",
            "otacerts.zip"
        ]
        self.optional_files = [
            "vbmeta.img"  # vbmeta镜像为可选，部分设备可能不存在此分区
        ]
        
    def run_adb_command(self, command, check_output=False, timeout=30):
        """执行ADB命令 - ACE优化：抑制console输出"""
        try:
            print(f"执行命令: {command}")
            if check_output:
                result = subprocess.run(
                    command, shell=True, capture_output=True,
                    text=True, timeout=timeout,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                print(f"输出: {result.stdout.strip()}")
                if result.stderr:
                    print(f"错误: {result.stderr.strip()}")
                return result
            else:
                result = subprocess.run(
                    command, shell=True, timeout=timeout,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                return result
        except subprocess.TimeoutExpired:
            print(f"命令超时: {command}")
            return None
        except Exception as e:
            print(f"执行命令失败: {e}")
            return None
    
    def check_files_exist(self):
        """检查所需文件是否存在 - ACE优化：支持可选文件检查"""
        print("检查所需文件...")
        missing_files = []

        # 检查必需文件
        for file_name in self.required_files:
            file_path = self.files_dir / file_name
            if not file_path.exists():
                missing_files.append(str(file_path))
            else:
                print(f"✓ 找到必需文件: {file_path}")

        # 检查可选文件
        optional_status = {}
        for file_name in self.optional_files:
            file_path = self.files_dir / file_name
            if file_path.exists():
                print(f"✓ 找到可选文件: {file_path}")
                optional_status[file_name] = True
            else:
                print(f"⚠️ 可选文件不存在: {file_path}")
                optional_status[file_name] = False

        # 保存可选文件状态供后续使用
        self.optional_file_status = optional_status

        if missing_files:
            print(f"❌ 缺少必需文件: {missing_files}")
            return False
        print("✓ 所有必需文件都存在")
        return True
    
    def wait_for_device(self):
        """等待设备连接"""
        print("等待设备连接...")
        result = self.run_adb_command("adb wait-for-device", timeout=60)
        if result and result.returncode == 0:
            print("✓ 设备已连接")
            return True
        print("❌ 设备连接失败")
        return False
    
    def get_slot_suffix(self):
        """获取当前活动槽位"""
        print("获取当前活动槽位...")
        result = self.run_adb_command("adb shell getprop ro.boot.slot_suffix", check_output=True)
        if result and result.returncode == 0:
            slot = result.stdout.strip()
            print(f"当前活动槽位: {slot}")
            return slot
        print("❌ 无法获取槽位信息")
        return None
    
    def get_partition_mapping(self):
        """获取分区映射信息"""
        print("获取分区映射...")
        result = self.run_adb_command("adb shell ls -l /dev/block/by-name", check_output=True)
        if result and result.returncode == 0:
            print("分区映射信息:")
            print(result.stdout)
            return self.parse_partition_mapping(result.stdout)
        print("❌ 无法获取分区映射")
        return None

    def parse_partition_mapping(self, mapping_output):
        """解析分区映射输出"""
        partitions = {}
        for line in mapping_output.strip().split('\n'):
            if '->' in line:
                parts = line.split()
                if len(parts) >= 3:
                    partition_name = parts[-3]
                    device_path = parts[-1]
                    partitions[partition_name] = device_path
        return partitions

    def get_active_partition_path(self, partitions, partition_base, slot_suffix):
        """获取活动分区路径"""
        if slot_suffix:
            partition_name = f"{partition_base}{slot_suffix}"
        else:
            partition_name = partition_base

        if partition_name in partitions:
            return partitions[partition_name]

        # 如果没找到带槽位的分区，尝试不带槽位的
        if partition_base in partitions:
            return partitions[partition_base]

        return None

    def update_apk_files(self):
        """执行APK文件更新流程 - ACE优化：四代357专用APK推送"""
        print("=" * 50)
        print("开始APK文件更新流程 (奔腾D357_4)")
        print("=" * 50)

        # 1. 检查APK文件
        if not self.check_apk_files_exist():
            return False

        # 2. 等待设备
        if not self.wait_for_device():
            return False

        # 3. 获取root权限
        print("\n步骤1: 获取root权限...")
        result = self.run_adb_command("adb root")
        if result is None:
            return False
        time.sleep(2)

        # 4. 尝试remount，可能需要禁用verity
        print("\n步骤2: 尝试remount...")
        result = self.run_adb_command("adb remount", check_output=True)
        if result and "dm_verity is enabled" in result.stderr:
            print("需要禁用verity...")

            # 禁用verity
            result = self.run_adb_command("adb disable-verity")
            if result is None:
                return False

            print("重启设备以应用verity设置...")
            self.run_adb_command("adb reboot")
            time.sleep(30)  # 等待重启

            # 重新获取root权限
            if not self.wait_for_device():
                return False
            self.run_adb_command("adb root")
            time.sleep(2)

            # 再次尝试remount
            result = self.run_adb_command("adb remount")
            if result is None:
                return False

        # 5. 推送APK文件
        print("\n步骤3: 推送APK文件...")
        if not self.push_apk_files():
            return False

        # 6. 最终重启
        print("\n步骤4: 重启设备...")
        self.run_adb_command("adb reboot")

        print("\n" + "=" * 50)
        print("APK文件更新流程完成！")
        print("设备正在重启，请等待启动完成。")
        print("=" * 50)

        return True

    def check_apk_files_exist(self):
        """检查APK文件是否存在 - ACE优化：四代357专用"""
        print("检查APK文件...")

        # 定义APK文件映射
        apk_files = {
            "BDSettings-aligned-signed.apk": "signed/BDSettings-aligned-signed.apk",
            "BtPhone-aligned-signed.apk": "signed/BtPhone-aligned-signed.apk",
            "Hvac-aligned-signed.apk": "signed/Hvac-aligned-signed.apk",
            "LauncherApp-aligned-signed.apk": "signed/LauncherApp-aligned-signed.apk",
            "MusicApp-aligned-signed.apk": "signed/MusicApp-aligned-signed.apk",
            "RadioApp-aligned-signed.apk": "signed/RadioApp-aligned-signed.apk",
            "UsbApp-aligned-signed.apk": "signed/UsbApp-aligned-signed.apk",
            "VehicleSettings-aligned-signed.apk": "signed/VehicleSettings-aligned-signed.apk",
            "XCNotificationCenterUI-aligned-signed.apk": "signed/XCNotificationCenterUI-aligned-signed.apk",
            "CarService-aligned-signed.apk": "signed/CarService-aligned-signed.apk"
        }

        missing_files = []
        for apk_name, apk_path in apk_files.items():
            file_path = self.files_dir / apk_path
            if not file_path.exists():
                missing_files.append(str(file_path))
            else:
                print(f"✓ 找到APK文件: {file_path}")

        # 检查CarShare目录
        carshare_dir = self.files_dir / "CarShare"
        if not carshare_dir.exists():
            missing_files.append(str(carshare_dir))
        else:
            print(f"✓ 找到CarShare目录: {carshare_dir}")

        if missing_files:
            print(f"❌ 缺少APK文件: {missing_files}")
            return False
        print("✓ 所有APK文件都存在")
        return True

    def push_apk_files(self):
        """推送APK文件到系统目录 - ACE优化：四代357专用"""
        # 定义APK文件和目标路径映射
        apk_mappings = [
            ("signed/BDSettings-aligned-signed.apk", "/system/app/BDSettings/BDSettings.apk"),
            ("signed/BtPhone-aligned-signed.apk", "/system/app/BtPhone/BtPhone.apk"),
            ("signed/Hvac-aligned-signed.apk", "/system/app/Hvac/Hvac.apk"),
            ("signed/LauncherApp-aligned-signed.apk", "/system/app/LauncherApp/LauncherApp.apk"),
            ("signed/MusicApp-aligned-signed.apk", "/system/app/MusicApp/MusicApp.apk"),
            ("signed/RadioApp-aligned-signed.apk", "/system/app/RadioApp/RadioApp.apk"),
            ("signed/UsbApp-aligned-signed.apk", "/system/app/UsbApp/UsbApp.apk"),
            ("signed/VehicleSettings-aligned-signed.apk", "/system/app/VehicleSettings/VehicleSettings.apk"),
            ("signed/XCNotificationCenterUI-aligned-signed.apk", "/system/app/XCNotificationCenterUI/XCNotificationCenterUI.apk"),
            ("signed/CarService-aligned-signed.apk", "/system/priv-app/CarService/CarService.apk")
        ]

        # 推送APK文件
        for local_path, target_path in apk_mappings:
            apk_file = self.files_dir / local_path
            apk_name = local_path.split('/')[-1].replace('-aligned-signed.apk', '')

            print(f"推送 {apk_name}...")
            result = self.run_adb_command(f"adb push {apk_file} {target_path}")
            if result is None or result.returncode != 0:
                print(f"⚠️ 推送 {apk_name} 失败")
                # 继续推送其他文件，不中断流程
            else:
                print(f"✓ {apk_name} 推送成功")

        # 推送CarShare目录
        carshare_dir = self.files_dir / "CarShare"
        print("推送 CarShare 目录...")
        result = self.run_adb_command(f"adb push {carshare_dir} /system/app")
        if result is None or result.returncode != 0:
            print("⚠️ 推送 CarShare 目录失败")
        else:
            print("✓ CarShare 目录推送成功")

        return True  # 即使部分文件失败也返回True，让流程继续

    def check_vbmeta_partition_exists(self, partitions, slot_suffix):
        """检查vbmeta分区是否存在 - ACE优化：兼容不存在vbmeta分区的设备"""
        vbmeta_partition = self.get_active_partition_path(partitions, "vbmeta", slot_suffix)
        if vbmeta_partition:
            print(f"✓ 找到vbmeta分区: {vbmeta_partition}")
            return vbmeta_partition
        else:
            print("⚠️ 设备不存在vbmeta分区，将跳过vbmeta相关操作")
            return None

    def update_ota_signature(self):
        """执行完整的OTA签名更新流程"""
        print("=" * 50)
        print("开始OTA签名更新流程")
        print("=" * 50)
        
        # 1. 检查文件
        if not self.check_files_exist():
            return False
        
        # 2. 等待设备
        if not self.wait_for_device():
            return False
        
        # 3. 获取root权限
        print("\n步骤1: 获取root权限...")
        result = self.run_adb_command("adb root")
        if result is None:
            return False
        time.sleep(2)
        
        # 4. 尝试remount，可能需要禁用verity
        print("\n步骤2: 尝试remount...")
        result = self.run_adb_command("adb remount", check_output=True)
        if result and "dm_verity is enabled" in result.stderr:
            print("需要禁用verity...")
            
            # 禁用verity
            result = self.run_adb_command("adb disable-verity")
            if result is None:
                return False
            
            print("重启设备以应用verity设置...")
            self.run_adb_command("adb reboot")
            time.sleep(30)  # 等待重启
            
            # 重新获取root权限
            if not self.wait_for_device():
                return False
            self.run_adb_command("adb root")
            time.sleep(2)
            
            # 再次尝试remount
            result = self.run_adb_command("adb remount")
            if result is None:
                return False
        
        # 5. 推送镜像文件到临时目录
        print("\n步骤3: 推送镜像文件...")
        boot_img = self.files_dir / "boot.img"

        # 推送boot镜像（必需）
        result = self.run_adb_command(f"adb push {boot_img} /data/local/tmp/")
        if result is None or result.returncode != 0:
            return False

        # ACE优化：条件推送vbmeta镜像（仅当文件存在时）
        vbmeta_available = self.optional_file_status.get("vbmeta.img", False)
        if vbmeta_available:
            vbmeta_img = self.files_dir / "vbmeta.img"
            result = self.run_adb_command(f"adb push {vbmeta_img} /data/local/tmp/")
            if result is None or result.returncode != 0:
                print("❌ 推送vbmeta镜像失败")
                return False
            print("✓ vbmeta镜像推送成功")
        else:
            print("⚠️ 跳过vbmeta镜像推送（文件不存在）")
        
        # 6. 获取槽位和分区信息
        print("\n步骤4: 获取设备信息...")
        slot_suffix = self.get_slot_suffix()
        partitions = self.get_partition_mapping()

        if not partitions:
            print("❌ 无法获取分区信息")
            return False

        # 7. 写入镜像到分区
        print("\n步骤5: 写入镜像到分区...")

        # 获取boot分区路径
        boot_partition = self.get_active_partition_path(partitions, "boot", slot_suffix)
        if not boot_partition:
            print("❌ 无法找到boot分区")
            return False
        print(f"Boot分区路径: {boot_partition}")

        # ACE优化：检查vbmeta分区是否存在（兼容性处理）
        vbmeta_partition = self.check_vbmeta_partition_exists(partitions, slot_suffix)
        vbmeta_partition_available = vbmeta_partition is not None

        # 写入boot镜像（必需操作）
        print("正在写入boot镜像...")
        result = self.run_adb_command(f"adb shell dd if=/data/local/tmp/boot.img of={boot_partition}")
        if result is None or result.returncode != 0:
            print("❌ 写入boot镜像失败")
            return False
        print("✓ boot镜像写入成功")

        # ACE优化：条件写入vbmeta镜像（仅当分区和文件都存在时）
        if vbmeta_partition_available and vbmeta_available:
            print("正在写入vbmeta镜像...")
            result = self.run_adb_command(f"adb shell dd if=/data/local/tmp/vbmeta.img of={vbmeta_partition}")
            if result is None or result.returncode != 0:
                print("❌ 写入vbmeta镜像失败")
                return False
            print("✓ vbmeta镜像写入成功")
        elif not vbmeta_partition_available:
            print("⚠️ 跳过vbmeta镜像写入（设备不存在vbmeta分区）")
        elif not vbmeta_available:
            print("⚠️ 跳过vbmeta镜像写入（vbmeta.img文件不存在）")
        else:
            print("⚠️ 跳过vbmeta镜像写入（分区或文件不可用）")
        
        # 8. 推送OTA签名文件
        print("\n步骤6: 推送OTA签名文件...")
        key_file = self.files_dir / "update-payload-key.pub.pem"
        certs_file = self.files_dir / "otacerts.zip"
        
        # 推送公钥文件
        self.run_adb_command(f"adb push {key_file} /etc/update_engine/update-payload-key.pub.pem")
        self.run_adb_command(f"adb push {key_file} /system/etc/update_engine/update-payload-key.pub.pem")
        
        # 推送证书文件
        self.run_adb_command(f"adb push {certs_file} /etc/security/otacerts.zip")
        self.run_adb_command(f"adb push {certs_file} /system/etc/security/otacerts.zip")
        
        # 9. 最终重启
        print("\n步骤7: 重启设备...")
        self.run_adb_command("adb reboot")
        
        print("\n" + "=" * 50)
        print("OTA签名更新流程完成！")
        print("设备正在重启，请等待启动完成。")
        print("=" * 50)
        
        return True


def main():
    """主函数"""
    print("Android OTA签名自动更新工具")
    print("警告: 此操作会修改系统分区，请确保您了解风险！")
    
    # 确认操作
    response = input("是否继续？(y/N): ").strip().lower()
    if response != 'y':
        print("操作已取消")
        return
    
    # 创建ADB管理器并执行更新
    adb_manager = ADBManager()
    success = adb_manager.update_ota_signature()
    
    if success:
        print("✓ 更新完成")
        sys.exit(0)
    else:
        print("❌ 更新失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
