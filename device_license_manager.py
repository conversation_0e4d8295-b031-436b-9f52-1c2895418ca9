#!/usr/bin/env python3
"""
设备端一机一码授权管理模块
实现设备序列号的RSA签名验证功能
ACE实现：设备端授权验证，基于序列号的RSA验签
"""

import json
import base64
import hashlib
from pathlib import Path
from typing import Tuple
from datetime import datetime

# RSA加密相关导入
try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.backends import default_backend
    RSA_AVAILABLE = True
except ImportError:
    RSA_AVAILABLE = False


class DeviceLicenseManager:
    """设备端授权管理类 - ACE实现：基于序列号的RSA验签"""
    
    def __init__(self):
        """初始化设备授权管理器"""
        # RSA公钥（内置在程序中，用于验签设备授权）
        self.device_rsa_public_key_pem = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuBKfdrEbR5atPp7wIvin
d0s+hIYzeUlHAIep63sTJ3GdMrEUnGI4BeggJgG8D9S+SRoZeIXsrEv5qz6feIuh
ILjRbJgOl6IF4nB5AI90vm3+/Ay35DjI9C149aKx9NMVP1T8LZEPBZj21A2fbJ9s
hgIZCdIxyjuUbQcHt5nUt6EcvpWd3A515IufOlvchxMZO5lTobt+lvVEt5pl7xww
XwXFK0+wB3HCoAR+E7zxvrU9oI/T0YiLMk2LMICkFBuQqfZUn/7kbAwAy1d47tjn
+zwu2tgKqYsh19qBMwCMtnWzV+FcCh2DeBYuMxzc+t9LNMbd0owtvqMJkxBlDrso
fwIDAQAB
-----END PUBLIC KEY-----"""
    
    def _get_license_file_path(self, serial_number: str) -> Path:
        """获取设备授权文件路径"""
        # 授权文件格式：序列号.dat
        license_filename = f"{serial_number}.dat"
        
        # 尝试多个可能的路径
        possible_paths = [
            Path.cwd() / license_filename,  # 当前工作目录
            Path(__file__).parent / license_filename,  # 脚本同级目录
            Path("licenses") / license_filename,  # licenses子目录
        ]
        
        # 返回第一个存在的文件，或默认路径
        for path in possible_paths:
            if path.exists():
                return path
        
        return possible_paths[0]  # 默认返回当前目录
    
    def _load_device_rsa_public_key(self):
        """加载设备RSA公钥"""
        if not RSA_AVAILABLE:
            raise ImportError("cryptography库未安装，无法使用RSA功能")
        
        try:
            public_key = serialization.load_pem_public_key(
                self.device_rsa_public_key_pem.encode(),
                backend=default_backend()
            )
            return public_key
        except Exception as e:
            raise ValueError(f"加载设备RSA公钥失败: {str(e)}")
    
    def _verify_device_rsa_signature(self, message: str, signature: str) -> bool:
        """验证设备RSA签名"""
        if not RSA_AVAILABLE:
            return False
        
        try:
            # 加载公钥
            public_key = self._load_device_rsa_public_key()
            
            # 解码签名
            signature_bytes = base64.b64decode(signature)
            
            # 验证签名
            public_key.verify(
                signature_bytes,
                message.encode('utf-8'),
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except Exception:
            return False
    
    def verify_device_license(self, serial_number: str) -> Tuple[bool, str]:
        """验证设备授权 - ACE实现：基于序列号的RSA验签"""
        if not serial_number or serial_number.strip() == "":
            return False, "设备序列号为空"
        
        # 清理序列号（移除空格和特殊字符）
        clean_serial = serial_number.strip().upper()
        
        try:
            # 获取授权文件路径
            license_file = self._get_license_file_path(clean_serial)
            
            if not license_file.exists():
                return False, f"设备未授权\n授权文件不存在: {license_file.name}"
            
            # 读取授权文件
            try:
                with open(license_file, 'r', encoding='utf-8') as f:
                    license_key = f.read().strip()
            except Exception as read_error:
                return False, f"读取设备授权文件失败: {str(read_error)}"
            
            # 解码授权码
            try:
                license_content = json.loads(
                    base64.b64decode(license_key.encode()).decode()
                )
            except:
                return False, "设备授权码格式错误"
            
            data = license_content.get('data', {})
            signature = license_content.get('signature', '')
            
            # ACE优化：使用RSA签名验证
            if RSA_AVAILABLE:
                # RSA签名验证
                data_str = json.dumps(data, sort_keys=True)
                if not self._verify_device_rsa_signature(data_str, signature):
                    return False, "设备RSA签名验证失败"
            else:
                return False, "RSA验证库不可用，无法验证设备授权"
            
            # 验证序列号匹配
            authorized_serial = data.get('serial_number', '')
            if authorized_serial != clean_serial:
                return False, f"设备序列号不匹配\n授权序列号: {authorized_serial}\n当前序列号: {clean_serial}"
            
            # 验证过期时间
            expire_timestamp = data.get('expire_timestamp', 0)
            if datetime.now().timestamp() > expire_timestamp:
                expire_date = datetime.fromtimestamp(expire_timestamp)
                return False, f"设备授权已过期\n过期时间: {expire_date.strftime('%Y-%m-%d %H:%M:%S')}"
            
            # 验证成功
            expire_date = datetime.fromtimestamp(expire_timestamp)
            device_name = data.get('device_name', '未知设备')
            return True, f"设备授权验证成功\n设备名称: {device_name}\n序列号: {clean_serial}\n过期时间: {expire_date.strftime('%Y-%m-%d %H:%M:%S')}"
            
        except Exception as e:
            return False, f"设备授权验证异常: {str(e)}"
    
    def get_device_license_info(self, serial_number: str) -> dict:
        """获取设备授权信息"""
        if not serial_number or serial_number.strip() == "":
            return {
                'serial_number': '',
                'license_status': '序列号为空',
                'license_details': '无法获取设备授权信息',
                'license_file_exists': False
            }
        
        clean_serial = serial_number.strip().upper()
        license_file = self._get_license_file_path(clean_serial)
        
        # 验证授权
        is_valid, message = self.verify_device_license(clean_serial)
        
        return {
            'serial_number': clean_serial,
            'license_status': '已授权' if is_valid else '未授权',
            'license_details': message,
            'license_file_exists': license_file.exists(),
            'license_file_path': str(license_file)
        }
    
    def check_device_authorization_status(self, serial_number: str) -> bool:
        """检查设备授权状态（简化版本，仅返回True/False）"""
        if not serial_number or serial_number.strip() == "":
            return False
        
        is_valid, _ = self.verify_device_license(serial_number)
        return is_valid


# 测试函数
def test_device_license_system():
    """测试设备授权系统"""
    print("🔐 测试设备端一机一码授权系统")
    print("=" * 50)
    
    dlm = DeviceLicenseManager()
    
    # 测试序列号
    test_serial = "TEST123456789"
    
    print(f"测试序列号: {test_serial}")
    
    # 验证设备授权
    is_valid, message = dlm.verify_device_license(test_serial)
    print(f"验证结果: {is_valid}")
    print(f"验证信息: {message}")
    
    # 获取设备授权信息
    info = dlm.get_device_license_info(test_serial)
    print("\n设备授权信息:")
    for key, value in info.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    test_device_license_system()
