#!/usr/bin/env python3
"""
Android 秘钥更新工具 - PySide6现代化版本
使用PySide6重新实现，解决所有tkinter兼容性问题
ACE优化：完全禁用console输出
"""

import sys
import os
import time
import subprocess
import threading
from pathlib import Path
from dataclasses import dataclass
from typing import Optional

# ACE优化：完全抑制导入时的console输出
import builtins
import sys
import os

# 保存原始print函数
_original_print = builtins.print

# 创建完全静默的print函数用于导入阶段
def _silent_print(*args, **kwargs):
    """完全静默的print函数，防止导入时console弹出"""
    pass

# 在导入期间完全禁用print
builtins.print = _silent_print

# 导入授权管理模块（静默导入）
from license_manager import LicenseManager
from license_dialog import show_license_dialog
from device_license_manager import DeviceLicenseManager

# 导入OTA更新模块（静默导入）
from update_ota_signature import ADBManager

# 导入完成后，创建GUI友好的print函数
def _gui_print(*args, **kwargs):
    """GUI友好的print函数，只在GUI模式下输出"""
    if hasattr(sys, '_gui_mode') and sys._gui_mode:
        _original_print(*args, **kwargs)
    # 否则保持静默

# 设置GUI友好的print函数
builtins.print = _gui_print

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QGroupBox, QLineEdit, QFileDialog,
    QMessageBox, QSplitter, QFrame, QScrollArea, QProgressBar,
    QStatusBar, QMenuBar, QToolBar, QTabWidget, QComboBox
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QSize
from PySide6.QtGui import QFont, QIcon, QPixmap, QAction, QPalette, QColor, QClipboard
import io
import contextlib


@dataclass
class DeviceInfo:
    """设备信息数据类"""
    model: str = ""
    android_version: str = ""
    slot_suffix: str = ""
    serial_number: str = ""
    connected: bool = False
    authorized: bool = False
    rooted: bool = False
    last_update_time: str = ""


class OTAUpdateThread(QThread):
    """OTA更新线程 - ACE优化：支持不同车型的更新方法"""
    log_message = Signal(str, str)  # message, level
    update_finished = Signal(bool)  # success

    def __init__(self, files_dir, vehicle_model="奔腾D357_3"):
        super().__init__()
        self.files_dir = files_dir
        self.vehicle_model = vehicle_model

    def run(self):
        """执行OTA更新"""
        try:
            # 创建ADB管理器
            adb_manager = ADBManager(str(self.files_dir))

            # 重定向ADBManager的print输出到信号
            original_print = print

            def gui_print(*args, **kwargs):
                message = ' '.join(str(arg) for arg in args)
                # 根据消息内容判断日志级别
                if message.startswith('✓') or '完成' in message or '成功' in message:
                    level = "success"
                elif message.startswith('❌') or '失败' in message or '错误' in message:
                    level = "error"
                elif message.startswith('⚠️') or '警告' in message:
                    level = "warning"
                elif message.startswith('步骤') or '开始' in message:
                    level = "step"
                else:
                    level = "info"
                self.log_message.emit(message, level)

            # 替换print函数
            import builtins
            builtins.print = gui_print

            try:
                # ACE优化：根据车型选择不同的更新方法
                if self.vehicle_model == "奔腾D357_4":
                    # 四代357使用APK更新方法
                    success = adb_manager.update_apk_files()
                else:
                    # 三代357使用OTA签名更新方法
                    success = adb_manager.update_ota_signature()
                self.update_finished.emit(success)
            finally:
                # 恢复原始print函数
                builtins.print = original_print

        except Exception as e:
            self.log_message.emit(f"❌ OTA更新过程中发生异常: {str(e)}", "error")
            self.update_finished.emit(False)


class DeviceMonitorThread(QThread):
    """设备监控线程"""
    device_updated = Signal(DeviceInfo)

    def __init__(self):
        super().__init__()
        self.running = True
        
    def run(self):
        """运行设备监控"""
        while self.running:
            device_info = self.check_device_status()
            self.device_updated.emit(device_info)
            self.msleep(3000)  # 每3秒检查一次
    
    def check_device_status(self) -> DeviceInfo:
        """检查设备状态"""
        device_info = DeviceInfo()
        
        try:
            # 检查设备连接 - ACE优化：完全抑制console
            result = subprocess.run(
                ["adb", "devices"],
                capture_output=True,
                text=True,
                timeout=5,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                device_lines = [line for line in lines[1:] if line.strip() and not line.startswith('*')]
                
                if device_lines:
                    device_info.connected = True
                    device_info.authorized = "unauthorized" not in result.stdout
                    
                    if device_info.authorized:
                        # 获取设备信息
                        device_info.serial_number = self.get_device_property("ro.serialno")
                        device_info.model = self.get_device_property("ro.product.model")
                        device_info.android_version = self.get_device_property("ro.build.version.release")
                        device_info.slot_suffix = self.get_device_property("ro.boot.slot_suffix")
                        
                        # 检查root权限 - ACE优化：完全抑制console
                        root_result = subprocess.run(
                            ["adb", "shell", "id"],
                            capture_output=True,
                            text=True,
                            timeout=5,
                            creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                        )
                        device_info.rooted = root_result.returncode == 0 and "uid=0" in root_result.stdout
                        
            device_info.last_update_time = time.strftime('%H:%M:%S')
            
        except Exception as e:
            # ACE优化：使用静默处理，不输出到console
            pass

        return device_info
    
    def get_device_property(self, prop_name: str) -> str:
        """获取设备属性 - ACE优化：完全抑制console"""
        try:
            result = subprocess.run(
                ["adb", "shell", "getprop", prop_name],
                capture_output=True,
                text=True,
                timeout=5,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )
            return result.stdout.strip() if result.returncode == 0 else ""
        except:
            return ""
    
    def stop(self):
        """停止监控"""
        self.running = False


class ModernOTAGUI(QMainWindow):
    """现代化OTA更新GUI主窗口"""

    def __init__(self):
        super().__init__()

        # 初始化授权管理器
        self.license_manager = LicenseManager()

        # 初始化设备授权管理器 - ACE实现：设备端一机一码
        self.device_license_manager = DeviceLicenseManager()

        # 初始化数据
        self.device_info = DeviceInfo()

        # ACE优化：车型配置管理
        self.vehicle_models = {
            "奔腾D357_3": "files",
            "奔腾D357_4": "d357_4"
        }
        self.current_vehicle_model = "奔腾D357_3"  # 默认车型
        self.files_dir = self.get_resource_path(self.vehicle_models[self.current_vehicle_model])

        # ACE优化：根据车型设置不同的文件要求
        self.update_file_requirements()

        # 设备连接状态跟踪
        self.device_connection_logged = False  # 跟踪是否已记录设备连接成功

        # 设置窗口
        self.setup_window()

        # 创建界面
        self.create_ui()

        # 启动设备监控
        self.start_device_monitoring()

        # 添加初始日志
        self.add_log("🚀 通用车机刷机工具已启动", "success")
        self.add_log("🔐 软件已通过授权验证", "success")
        self.add_log("📱 等待设备连接...", "info")

    def get_resource_path(self, relative_path):
        """获取资源文件路径 - ACE优化：支持打包后的资源访问"""
        try:
            # 检查是否在nuitka打包环境中
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller打包环境
                base_path = sys._MEIPASS
            elif hasattr(sys, 'frozen') and hasattr(sys, '_MEIPASS2'):
                # 其他打包环境
                base_path = sys._MEIPASS2
            elif getattr(sys, 'frozen', False):
                # Nuitka或其他打包环境
                base_path = Path(sys.executable).parent
            else:
                # 开发环境
                base_path = Path(__file__).parent

            resource_path = Path(base_path) / relative_path

            # 如果打包后的路径不存在，尝试exe同目录
            if not resource_path.exists() and getattr(sys, 'frozen', False):
                exe_dir = Path(sys.executable).parent
                resource_path = exe_dir / relative_path

            # 如果还是不存在，使用当前工作目录
            if not resource_path.exists():
                resource_path = Path.cwd() / relative_path

            return resource_path

        except Exception as e:
            # 出错时返回相对路径
            return Path(relative_path)

    def setup_window(self):
        """设置窗口属性"""
        # 获取授权信息用于标题显示
        license_info = self.license_manager.get_license_info()
        machine_code = license_info['machine_code']

        self.setWindowTitle(f"通用车机刷机工具 - 已授权 [{machine_code}]")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 600)

        # 设置应用图标（如果有的话）- ACE优化：支持多种图标格式和路径
        self.set_application_icon()
        
        # 设置现代化样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #1e1e1e;
                color: #ffffff;
                font-family: 'Monaco', 'Menlo', monospace;
            }
        """)

    def get_application_icon(self):
        """获取应用程序图标 - ACE优化：通用图标获取方法"""
        try:
            # 尝试多种图标文件和路径
            icon_candidates = [
                "icon.ico",      # 首选ICO格式（与构建脚本一致）
                "icon.png",      # 备选PNG格式
                self.get_resource_path("icon.ico"),   # 资源路径中的ICO
                self.get_resource_path("icon.png"),   # 资源路径中的PNG
            ]

            for icon_path in icon_candidates:
                try:
                    if Path(icon_path).exists():
                        icon = QIcon(str(icon_path))
                        if not icon.isNull():
                            return icon
                except Exception as e:
                    continue

            # 如果没有找到图标，返回空图标
            return QIcon()

        except Exception as e:
            print(f"[ERROR] Failed to get application icon: {e}")
            return QIcon()

    def set_application_icon(self):
        """设置应用程序图标 - ACE优化：支持多种图标格式和路径"""
        try:
            icon = self.get_application_icon()
            if not icon.isNull():
                self.setWindowIcon(icon)
                print(f"[INFO] Application icon set successfully")
            else:
                print("[WARNING] No valid icon file found, using default icon")

        except Exception as e:
            print(f"[ERROR] Failed to set application icon: {e}")

    def set_dialog_icon(self, dialog):
        """为对话框设置图标 - ACE优化：统一对话框图标设置"""
        try:
            icon = self.get_application_icon()
            if not icon.isNull():
                dialog.setWindowIcon(icon)
        except Exception as e:
            print(f"[ERROR] Failed to set dialog icon: {e}")

    def create_ui(self):
        """创建用户界面"""
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
        
        # 创建菜单栏
        self.create_menu_bar()

        # 添加授权信息到菜单
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_left_panel(self) -> QWidget:
        """创建左侧控制面板 - ACE优化：添加车型选择功能"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)

        # ACE优化：车型选择区域
        vehicle_group = self.create_vehicle_group()
        layout.addWidget(vehicle_group)

        # 文件管理区域
        file_group = self.create_file_group()
        layout.addWidget(file_group)

        # 设备状态区域
        device_group = self.create_device_group()
        layout.addWidget(device_group)

        # 操作按钮区域
        action_group = self.create_action_group()
        layout.addWidget(action_group)

        # 添加弹性空间
        layout.addStretch()

        return panel

    def update_file_requirements(self):
        """根据车型更新文件要求 - ACE优化：车型差异化文件管理"""
        if self.current_vehicle_model == "奔腾D357_4":
            # 四代357需要APK文件
            self.required_files = [
                "signed/BDSettings-aligned-signed.apk",
                "signed/BtPhone-aligned-signed.apk",
                "signed/Hvac-aligned-signed.apk",
                "signed/LauncherApp-aligned-signed.apk",
                "signed/MusicApp-aligned-signed.apk",
                "signed/RadioApp-aligned-signed.apk",
                "signed/UsbApp-aligned-signed.apk",
                "signed/VehicleSettings-aligned-signed.apk",
                "signed/XCNotificationCenterUI-aligned-signed.apk",
                "signed/CarService-aligned-signed.apk"
            ]
            self.optional_files = ["CarShare"]  # CarShare目录为可选
        else:
            # 三代357需要镜像文件
            self.required_files = ["boot.img", "update-payload-key.pub.pem", "otacerts.zip"]
            self.optional_files = ["vbmeta.img"]  # vbmeta镜像为可选，部分设备可能不存在此分区

    def create_vehicle_group(self) -> QGroupBox:
        """创建车型选择组 - ACE优化：车型切换功能"""
        group = QGroupBox("🚗 车型选择")
        layout = QVBoxLayout(group)
        layout.setSpacing(10)

        # 车型选择下拉框
        vehicle_layout = QHBoxLayout()

        vehicle_label = QLabel("当前车型:")
        vehicle_label.setStyleSheet("""
            font-weight: bold;
            color: #333;
            font-size: 13px;
        """)
        vehicle_layout.addWidget(vehicle_label)

        self.vehicle_combo = QComboBox()
        self.vehicle_combo.addItems(list(self.vehicle_models.keys()))
        self.vehicle_combo.setCurrentText(self.current_vehicle_model)
        self.vehicle_combo.currentTextChanged.connect(self.on_vehicle_changed)
        self.vehicle_combo.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 2px solid #ddd;
                border-radius: 6px;
                background-color: white;
                font-size: 13px;
                font-weight: bold;
                color: #333;
                min-width: 120px;
            }
            QComboBox:focus {
                border-color: #2196F3;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #ddd;
                background-color: white;
                selection-background-color: #e3f2fd;
            }
        """)
        vehicle_layout.addWidget(self.vehicle_combo)

        vehicle_layout.addStretch()
        layout.addLayout(vehicle_layout)

        # 车型状态显示
        self.vehicle_status_label = QLabel(f"✅ 已选择: {self.current_vehicle_model}")
        self.vehicle_status_label.setStyleSheet("""
            color: #4CAF50;
            font-weight: bold;
            font-size: 12px;
            padding: 6px 12px;
            background-color: rgba(76,175,80,0.15);
            border-radius: 4px;
            border: 1px solid rgba(76,175,80,0.3);
        """)
        layout.addWidget(self.vehicle_status_label)

        return group

    def create_file_group(self) -> QGroupBox:
        """创建文件管理组"""
        group = QGroupBox("📁 文件管理")
        layout = QVBoxLayout(group)

        # 文件目录选择
        dir_layout = QHBoxLayout()

        # self.files_dir_edit = QLineEdit(str(self.files_dir))
        # ACE优化：显示当前车型的文件目录
        self.files_dir_edit = QLineEdit(f"内置文件目录 ({self.current_vehicle_model})")
        self.files_dir_edit.setPlaceholderText("选择文件目录...")
        self.files_dir_edit.setReadOnly(True)  # 设为只读，防止用户误改
        self.files_dir_edit.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                color: #495057;
                border: 1px solid #ced4da;
            }
        """)
        dir_layout.addWidget(self.files_dir_edit)

        browse_btn = QPushButton("浏览")
        browse_btn.clicked.connect(self.browse_files_dir)
        browse_btn.setStyleSheet("QPushButton { background-color: #4CAF50; }")
        dir_layout.addWidget(browse_btn)

        layout.addLayout(dir_layout)

        # 文件状态显示区域
        self.file_status_widget = QWidget()
        self.file_status_layout = QVBoxLayout(self.file_status_widget)
        self.file_status_layout.setContentsMargins(0, 0, 0, 0)

        # 添加滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.file_status_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(120)
        scroll_area.setStyleSheet("QScrollArea { border: 1px solid #ddd; background-color: #fafafa; }")

        layout.addWidget(scroll_area)

        return group

    def create_device_group(self) -> QGroupBox:
        """创建设备状态组"""
        group = QGroupBox("📱 设备状态")
        layout = QVBoxLayout(group)

        # 基础状态
        status_frame = QFrame()
        status_layout = QVBoxLayout(status_frame)

        # 连接状态
        self.connection_label = self.create_status_label("连接状态", "未连接", "#F44336")
        status_layout.addWidget(self.connection_label)

        # 授权状态
        self.auth_label = self.create_status_label("授权状态", "未授权", "#F44336")
        status_layout.addWidget(self.auth_label)

        # Root权限
        self.root_label = self.create_status_label("Root权限", "无权限", "#F44336")
        status_layout.addWidget(self.root_label)

        # 设备授权状态 - ACE实现：设备端一机一码
        self.device_auth_label = self.create_status_label("设备授权", "未验证", "#FF9800")
        status_layout.addWidget(self.device_auth_label)

        layout.addWidget(status_frame)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("QFrame { color: #cccccc; }")
        layout.addWidget(line)

        # 详细信息
        detail_frame = QFrame()
        detail_layout = QVBoxLayout(detail_frame)

        # 设备信息 - 序列号特殊处理，添加复制按钮
        self.serial_label = self.create_serial_label("序列号", "未知")
        detail_layout.addWidget(self.serial_label)

        self.model_label = self.create_info_label("设备型号", "未知")
        detail_layout.addWidget(self.model_label)

        self.slot_label = self.create_info_label("活跃槽位", "未知")
        detail_layout.addWidget(self.slot_label)

        self.android_label = self.create_info_label("Android版本", "未知")
        detail_layout.addWidget(self.android_label)

        layout.addWidget(detail_frame)

        return group

    def create_status_label(self, title: str, value: str, color: str) -> QWidget:
        """创建状态标签"""
        widget = QWidget()
        widget.setMinimumHeight(35)  # 设置最小高度确保文字完整显示
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 8, 0, 8)  # 增加垂直边距
        layout.setSpacing(10)  # 设置组件间距

        title_label = QLabel(f"{title}:")
        title_label.setStyleSheet("""
            font-weight: bold;
            color: #333;
            font-size: 13px;
            padding: 2px 0px;
        """)
        title_label.setMinimumHeight(25)  # 确保标题标签高度
        layout.addWidget(title_label)

        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            color: {color};
            font-weight: bold;
            font-size: 12px;
            padding: 6px 12px;
            background-color: rgba(0,0,0,0.1);
            border-radius: 4px;
            min-height: 20px;
            qproperty-alignment: AlignCenter;
        """)
        value_label.setMinimumHeight(25)  # 确保值标签高度
        value_label.setMinimumWidth(80)   # 设置最小宽度
        layout.addWidget(value_label)

        layout.addStretch()

        # 保存value_label的引用以便后续更新
        widget.value_label = value_label

        return widget

    def create_info_label(self, title: str, value: str) -> QWidget:
        """创建信息标签"""
        widget = QWidget()
        widget.setMinimumHeight(28)  # 设置最小高度
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 5, 0, 5)  # 增加垂直边距
        layout.setSpacing(8)  # 设置组件间距

        title_label = QLabel(f"{title}:")
        title_label.setStyleSheet("""
            color: #666;
            font-size: 12px;
            padding: 2px 0px;
        """)
        title_label.setMinimumHeight(20)  # 确保标题标签高度
        layout.addWidget(title_label)

        value_label = QLabel(value)
        value_label.setStyleSheet("""
            color: #333;
            font-weight: bold;
            font-size: 12px;
            padding: 2px 6px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            min-height: 16px;
        """)
        value_label.setMinimumHeight(20)  # 确保值标签高度
        value_label.setMinimumWidth(100)  # 设置最小宽度
        layout.addWidget(value_label)

        layout.addStretch()

        # 保存value_label的引用以便后续更新
        widget.value_label = value_label

        return widget

    def create_serial_label(self, title: str, value: str) -> QWidget:
        """创建带复制按钮的序列号标签"""
        widget = QWidget()
        widget.setMinimumHeight(28)  # 设置最小高度
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 5, 0, 5)  # 增加垂直边距
        layout.setSpacing(8)  # 设置组件间距

        title_label = QLabel(f"{title}:")
        title_label.setStyleSheet("""
            color: #666;
            font-size: 12px;
            padding: 2px 0px;
        """)
        title_label.setMinimumHeight(20)  # 确保标题标签高度
        layout.addWidget(title_label)

        value_label = QLabel(value)
        value_label.setStyleSheet("""
            color: #333;
            font-weight: bold;
            font-size: 12px;
            padding: 2px 6px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            min-height: 16px;
        """)
        value_label.setMinimumHeight(20)  # 确保值标签高度
        value_label.setMinimumWidth(120)  # 序列号需要更宽
        layout.addWidget(value_label)

        # 添加复制按钮
        copy_btn = QPushButton("📋")
        copy_btn.setToolTip("复制序列号到剪切板")
        copy_btn.clicked.connect(lambda: self.copy_serial_to_clipboard())
        copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 12px;
                min-width: 25px;
                max-width: 25px;
                min-height: 20px;
                max-height: 20px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        layout.addWidget(copy_btn)

        layout.addStretch()

        # 保存value_label的引用以便后续更新
        widget.value_label = value_label
        widget.copy_btn = copy_btn  # 也保存复制按钮的引用

        return widget

    def create_action_group(self) -> QGroupBox:
        """创建操作按钮组"""
        group = QGroupBox("⚡ 操作")
        layout = QVBoxLayout(group)

        # 检查文件按钮
        check_files_btn = QPushButton("🔍 检查文件")
        check_files_btn.clicked.connect(self.check_files)
        check_files_btn.setStyleSheet("QPushButton { background-color: #2196F3; }")
        layout.addWidget(check_files_btn)

        # 刷新设备按钮
        refresh_btn = QPushButton("🔄 刷新设备")
        refresh_btn.clicked.connect(self.refresh_device)
        refresh_btn.setStyleSheet("QPushButton { background-color: #4CAF50; }")
        layout.addWidget(refresh_btn)

        # 开始更新按钮
        self.update_btn = QPushButton("🚀 开始更新")
        self.update_btn.clicked.connect(self.start_update)
        self.update_btn.setStyleSheet("QPushButton { background-color: #FF9800; }")
        self.update_btn.setEnabled(False)
        layout.addWidget(self.update_btn)

        # 获取分区信息按钮
        partition_btn = QPushButton("💾 获取分区信息")
        partition_btn.clicked.connect(self.get_partition_info)
        partition_btn.setStyleSheet("QPushButton { background-color: #9C27B0; }")
        layout.addWidget(partition_btn)

        return group

    def create_right_panel(self) -> QWidget:
        """创建右侧日志面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # 日志区域标题和工具栏
        header_layout = QHBoxLayout()

        title_label = QLabel("📋 执行日志")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #333;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 日志工具按钮
        clear_btn = QPushButton("🗑️ 清空")
        clear_btn.clicked.connect(self.clear_log)
        clear_btn.setStyleSheet("QPushButton { background-color: #F44336; padding: 6px 12px; }")
        header_layout.addWidget(clear_btn)

        save_btn = QPushButton("💾 保存")
        save_btn.clicked.connect(self.save_log)
        save_btn.setStyleSheet("QPushButton { background-color: #4CAF50; padding: 6px 12px; }")
        header_layout.addWidget(save_btn)

        layout.addLayout(header_layout)

        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        # 使用系统等宽字体
        font = QFont()
        font.setFamily("Monaco")  # macOS上的等宽字体
        font.setPointSize(10)
        font.setStyleHint(QFont.Monospace)
        self.log_text.setFont(font)
        layout.addWidget(self.log_text)

        return panel

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件")

        open_action = QAction("打开文件目录", self)
        open_action.triggered.connect(self.browse_files_dir)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 设备菜单
        device_menu = menubar.addMenu("设备")

        refresh_action = QAction("刷新设备状态", self)
        refresh_action.triggered.connect(self.refresh_device)
        device_menu.addAction(refresh_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助")

        # 授权信息菜单项
        license_action = QAction("🔐 授权信息", self)
        license_action.triggered.connect(self.show_license_info)
        help_menu.addAction(license_action)

        help_menu.addSeparator()

        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 添加永久状态信息
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)

        # 添加设备状态指示器
        self.device_status_label = QLabel("设备: 未连接")
        self.device_status_label.setStyleSheet("color: #F44336; font-weight: bold;")
        self.status_bar.addPermanentWidget(self.device_status_label)

    def start_device_monitoring(self):
        """启动设备监控"""
        self.device_thread = DeviceMonitorThread()
        self.device_thread.device_updated.connect(self.update_device_info)
        self.device_thread.start()

    def update_device_info(self, device_info: DeviceInfo):
        """更新设备信息显示"""
        # 检查设备连接状态变化
        was_connected = self.device_info.connected if hasattr(self, 'device_info') else False
        self.device_info = device_info

        # 更新基础状态
        if device_info.connected:
            # 如果设备刚刚连接成功，更新日志
            if not was_connected and not self.device_connection_logged:
                self.add_log("✅ 设备连接成功！", "success")
                self.device_connection_logged = True

            self.connection_label.value_label.setText("已连接")
            self.connection_label.value_label.setStyleSheet("""
                color: #4CAF50;
                font-weight: bold;
                font-size: 12px;
                padding: 6px 12px;
                background-color: rgba(76,175,80,0.15);
                border-radius: 4px;
                min-height: 20px;
                qproperty-alignment: AlignCenter;
            """)
            self.device_status_label.setText("设备: 已连接")
            self.device_status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        else:
            # 如果设备断开连接，重置连接状态标志
            if was_connected and self.device_connection_logged:
                self.add_log("⚠️ 设备连接已断开", "warning")
                self.device_connection_logged = False

            self.connection_label.value_label.setText("未连接")
            self.connection_label.value_label.setStyleSheet("""
                color: #F44336;
                font-weight: bold;
                font-size: 12px;
                padding: 6px 12px;
                background-color: rgba(244,67,54,0.15);
                border-radius: 4px;
                min-height: 20px;
                qproperty-alignment: AlignCenter;
            """)
            self.device_status_label.setText("设备: 未连接")
            self.device_status_label.setStyleSheet("color: #F44336; font-weight: bold;")

        if device_info.authorized:
            self.auth_label.value_label.setText("已授权")
            self.auth_label.value_label.setStyleSheet("""
                color: #4CAF50;
                font-weight: bold;
                font-size: 12px;
                padding: 6px 12px;
                background-color: rgba(76,175,80,0.15);
                border-radius: 4px;
                min-height: 20px;
                qproperty-alignment: AlignCenter;
            """)
        else:
            self.auth_label.value_label.setText("未授权")
            self.auth_label.value_label.setStyleSheet("""
                color: #F44336;
                font-weight: bold;
                font-size: 12px;
                padding: 6px 12px;
                background-color: rgba(244,67,54,0.15);
                border-radius: 4px;
                min-height: 20px;
                qproperty-alignment: AlignCenter;
            """)

        if device_info.rooted:
            self.root_label.value_label.setText("已获取")
            self.root_label.value_label.setStyleSheet("""
                color: #4CAF50;
                font-weight: bold;
                font-size: 12px;
                padding: 6px 12px;
                background-color: rgba(76,175,80,0.15);
                border-radius: 4px;
                min-height: 20px;
                qproperty-alignment: AlignCenter;
            """)
        else:
            self.root_label.value_label.setText("无权限")
            self.root_label.value_label.setStyleSheet("""
                color: #F44336;
                font-weight: bold;
                font-size: 12px;
                padding: 6px 12px;
                background-color: rgba(244,67,54,0.15);
                border-radius: 4px;
                min-height: 20px;
                qproperty-alignment: AlignCenter;
            """)

        # 更新详细信息
        self.serial_label.value_label.setText(device_info.serial_number or "未知")
        self.model_label.value_label.setText(device_info.model or "未知")
        self.slot_label.value_label.setText(device_info.slot_suffix or "未知")
        self.android_label.value_label.setText(device_info.android_version or "未知")

        # ACE实现：设备端一机一码授权验证
        device_authorized = False
        if device_info.connected and device_info.authorized and device_info.serial_number:
            # 验证设备授权
            device_authorized = self.device_license_manager.check_device_authorization_status(device_info.serial_number)

            if device_authorized:
                self.device_auth_label.value_label.setText("已授权")
                self.device_auth_label.value_label.setStyleSheet("""
                    color: #4CAF50;
                    font-weight: bold;
                    font-size: 12px;
                    padding: 6px 12px;
                    background-color: rgba(76,175,80,0.15);
                    border-radius: 4px;
                    min-height: 20px;
                    qproperty-alignment: AlignCenter;
                """)
            else:
                self.device_auth_label.value_label.setText("未授权")
                self.device_auth_label.value_label.setStyleSheet("""
                    color: #F44336;
                    font-weight: bold;
                    font-size: 12px;
                    padding: 6px 12px;
                    background-color: rgba(244,67,54,0.15);
                    border-radius: 4px;
                    min-height: 20px;
                    qproperty-alignment: AlignCenter;
                """)
        else:
            self.device_auth_label.value_label.setText("未验证")
            self.device_auth_label.value_label.setStyleSheet("""
                color: #FF9800;
                font-weight: bold;
                font-size: 12px;
                padding: 6px 12px;
                background-color: rgba(255,152,0,0.15);
                border-radius: 4px;
                min-height: 20px;
                qproperty-alignment: AlignCenter;
            """)

        # 更新按钮状态 - ACE优化：需要设备授权才能开始更新
        self.update_btn.setEnabled(device_info.connected and device_info.authorized and device_authorized)

    def add_log(self, message: str, level: str = "info"):
        """添加日志"""
        timestamp = time.strftime('%H:%M:%S')

        # 根据级别设置颜色
        color_map = {
            "info": "#ffffff",
            "success": "#4CAF50",
            "error": "#F44336",
            "warning": "#FF9800",
            "step": "#2196F3"
        }

        color = color_map.get(level, "#ffffff")

        # 格式化日志
        formatted_message = f'<span style="color: #888;">[{timestamp}]</span> <span style="color: {color};">{message}</span>'

        # 添加到日志区域
        self.log_text.append(formatted_message)

        # 滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def copy_serial_to_clipboard(self):
        """复制序列号到剪切板"""
        if self.device_info.serial_number and self.device_info.serial_number != "未知":
            clipboard = QApplication.clipboard()
            clipboard.setText(self.device_info.serial_number)
            self.add_log(f"📋 序列号已复制到剪切板: {self.device_info.serial_number}", "success")
            self.status_label.setText("序列号已复制到剪切板")
        else:
            self.add_log("⚠️ 没有可复制的序列号", "warning")
            self.status_label.setText("没有可复制的序列号")

    def show_device_unauthorized_dialog(self, auth_message: str):
        """显示设备未授权对话框 - ACE实现：设备端一机一码"""
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit

        # 创建设备未授权对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("🔒 设备授权验证")
        dialog.setFixedSize(500, 400)
        dialog.setModal(True)

        # ACE优化：设置对话框图标
        self.set_dialog_icon(dialog)

        # 设置样式
        dialog.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLabel {
                padding: 8px;
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 6px;
                color: #856404;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QTextEdit {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 10px;
                font-family: monospace;
                font-size: 12px;
            }
        """)

        layout = QVBoxLayout(dialog)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("🔒 设备未授权")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #F44336;
            background-color: #ffebee;
            border: 2px solid #F44336;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        """)
        layout.addWidget(title_label)

        # 设备信息
        device_info_label = QLabel(f"📱 设备序列号: {self.device_info.serial_number}")
        device_info_label.setStyleSheet("""
            font-family: monospace;
            font-weight: bold;
            color: #333;
            background-color: #e3f2fd;
            border: 1px solid #2196F3;
        """)
        layout.addWidget(device_info_label)

        # 错误信息
        error_label = QLabel("❌ 该设备尚未获得授权，无法进行OTA更新操作")
        error_label.setStyleSheet("""
            color: #F44336;
            font-weight: bold;
            background-color: #ffebee;
            border: 1px solid #F44336;
        """)
        layout.addWidget(error_label)

        # 详细信息
        details_text = QTextEdit()
        details_text.setPlainText(auth_message)
        details_text.setReadOnly(True)
        details_text.setMaximumHeight(120)
        layout.addWidget(details_text)

        # 说明信息
        instruction_label = QLabel(
            "💡 解决方案：\n"
            "1. 请联系管理员为此设备申请授权\n"
            "2. 提供设备序列号以获取授权文件\n"
            "3. 将授权文件放置在程序同级目录\n"
            "4. 授权文件格式：序列号.dat"
        )
        instruction_label.setStyleSheet("""
            color: #0277BD;
            background-color: #e1f5fe;
            border: 1px solid #0277BD;
            padding: 12px;
        """)
        instruction_label.setWordWrap(True)
        layout.addWidget(instruction_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 复制序列号按钮
        copy_btn = QPushButton("📋 复制序列号")
        copy_btn.clicked.connect(lambda: self.copy_serial_to_clipboard())
        copy_btn.setStyleSheet("background-color: #4CAF50;")
        button_layout.addWidget(copy_btn)

        button_layout.addStretch()

        # 确定按钮
        ok_btn = QPushButton("✅ 确定")
        ok_btn.clicked.connect(dialog.accept)
        button_layout.addWidget(ok_btn)

        layout.addLayout(button_layout)

        # 显示对话框
        dialog.exec()

        # 记录未授权尝试
        self.add_log(f"❌ 设备未授权，拒绝更新操作: {self.device_info.serial_number}", "error")

    def get_partition_info(self):
        """获取设备分区信息"""
        if not self.device_info.connected or not self.device_info.authorized:
            self.add_log("⚠️ 设备未连接或未授权，无法获取分区信息", "warning")
            return

        self.add_log("💾 开始获取设备分区信息...", "step")
        self.status_label.setText("正在获取分区信息...")

        try:
            # 获取分区表信息
            self.add_log("🔍 正在查询分区表...", "info")

            # 获取A/B分区信息
            ab_info = self.get_ab_partition_info()
            if ab_info:
                self.add_log("📋 A/B分区信息:", "step")
                for key, value in ab_info.items():
                    self.add_log(f"  {key}: {value}", "info")

            # 获取分区挂载点映射
            self.add_log("", "info")
            self.add_log("🔍 正在查询分区挂载点映射...", "info")
            partition_mapping = self.get_partition_mapping()

            if partition_mapping:
                self.add_log("📋 分区挂载点映射:", "step")
                for partition_name, mount_point in sorted(partition_mapping.items()):
                    self.add_log(f"  {partition_name} -> {mount_point}", "info")

            # 获取关键分区信息
            self.add_log("", "info")
            self.add_log("🔍 正在查询关键分区详细信息...", "info")
            key_partitions = ["boot", "system", "vendor", "product", "vbmeta", "recovery"]

            for partition in key_partitions:
                partition_info = self.get_partition_details(partition, partition_mapping)
                if partition_info:
                    self.add_log(f"📦 {partition} 分区:", "step")
                    for key, value in partition_info.items():
                        self.add_log(f"  {key}: {value}", "info")
                else:
                    self.add_log(f"❌ 未找到 {partition} 分区", "warning")

            # 获取存储信息
            self.add_log("", "info")
            self.add_log("🔍 正在查询存储信息...", "info")
            storage_info = self.get_storage_info()
            if storage_info:
                self.add_log("💾 存储信息:", "step")
                for key, value in storage_info.items():
                    self.add_log(f"  {key}: {value}", "info")

            # 显示完整分区表统计
            if partition_mapping:
                self.add_log("", "info")
                self.add_log("📊 分区表统计:", "step")
                total_partitions = len(partition_mapping)
                ab_partitions = len([name for name in partition_mapping.keys() if name.endswith('_a') or name.endswith('_b')])
                single_partitions = total_partitions - ab_partitions

                self.add_log(f"  总分区数量: {total_partitions}", "info")
                self.add_log(f"  A/B分区数量: {ab_partitions // 2 if ab_partitions > 0 else 0} 对", "info")
                self.add_log(f"  单一分区数量: {single_partitions}", "info")

                # 按类型分组显示
                boot_partitions = [name for name in partition_mapping.keys() if 'boot' in name.lower()]
                system_partitions = [name for name in partition_mapping.keys() if 'system' in name.lower()]
                vendor_partitions = [name for name in partition_mapping.keys() if 'vendor' in name.lower()]

                if boot_partitions:
                    self.add_log(f"  启动相关分区: {', '.join(boot_partitions)}", "info")
                if system_partitions:
                    self.add_log(f"  系统相关分区: {', '.join(system_partitions)}", "info")
                if vendor_partitions:
                    self.add_log(f"  厂商相关分区: {', '.join(vendor_partitions)}", "info")

            self.add_log("", "info")
            self.add_log("✅ 分区信息获取完成！", "success")
            self.status_label.setText("分区信息获取完成")

        except Exception as e:
            self.add_log(f"❌ 获取分区信息失败: {e}", "error")
            self.status_label.setText("分区信息获取失败")

    def get_ab_partition_info(self) -> dict:
        """获取A/B分区信息"""
        ab_info = {}

        try:
            # 获取当前活跃槽位
            current_slot = self.device_info.slot_suffix or "未知"
            ab_info["当前活跃槽位"] = current_slot

            # 获取槽位数量
            result = subprocess.run(
                ["adb", "shell", "getprop", "ro.boot.slot_suffix"],
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0 and result.stdout.strip():
                ab_info["支持A/B分区"] = "是"

                # 获取另一个槽位
                other_slot = "_b" if current_slot == "_a" else "_a"
                ab_info["备用槽位"] = other_slot

                # 检查槽位状态
                slot_info = self.check_slot_status()
                ab_info.update(slot_info)
            else:
                ab_info["支持A/B分区"] = "否"

        except Exception as e:
            ab_info["错误"] = str(e)

        return ab_info

    def check_slot_status(self) -> dict:
        """检查槽位状态"""
        slot_info = {}

        try:
            # 检查槽位是否可启动
            for slot in ["a", "b"]:
                result = subprocess.run(
                    ["adb", "shell", f"getprop ro.boot.slot_{slot}_bootable"],
                    capture_output=True, text=True, timeout=5
                )
                if result.returncode == 0:
                    bootable = result.stdout.strip()
                    slot_info[f"槽位{slot.upper()}可启动"] = "是" if bootable == "1" else "否"

        except Exception as e:
            slot_info["槽位状态错误"] = str(e)

        return slot_info

    def get_partition_mapping(self) -> dict:
        """获取分区挂载点映射"""
        partition_mapping = {}

        try:
            # 执行 ls -l /dev/block/by-name 命令
            result = subprocess.run(
                ["adb", "shell", "ls -l /dev/block/by-name"],
                capture_output=True, text=True, timeout=10
            )

            if result.returncode == 0:
                # 解析输出
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    # 跳过第一行（total 0）和空行
                    if line.startswith('total') or not line.strip():
                        continue

                    # 解析符号链接行
                    # 格式: lrwxrwxrwx 1 root root 20 1970-01-01 03:00 boot_a -> /dev/block/mmcblk0p3
                    if '->' in line:
                        parts = line.split()
                        if len(parts) >= 3:
                            # 获取分区名称（倒数第三个元素）
                            partition_name = parts[-3]
                            # 获取挂载点（最后一个元素）
                            mount_point = parts[-1]
                            partition_mapping[partition_name] = mount_point

        except Exception as e:
            self.add_log(f"❌ 获取分区映射失败: {e}", "error")

        return partition_mapping

    def get_partition_details(self, partition_name: str, partition_mapping: dict = None) -> dict:
        """获取指定分区的详细信息"""
        partition_info = {}

        try:
            # 如果有分区映射，使用映射信息
            if partition_mapping:
                # 查找匹配的分区
                matching_partitions = {}
                for name, mount_point in partition_mapping.items():
                    if name.startswith(partition_name):
                        matching_partitions[name] = mount_point

                if matching_partitions:
                    # 显示所有匹配的分区
                    if len(matching_partitions) == 1:
                        name, mount_point = list(matching_partitions.items())[0]
                        partition_info["分区名称"] = name
                        partition_info["挂载点"] = mount_point
                        partition_info["A/B分区"] = "否"
                    else:
                        # 多个分区（A/B分区）
                        partition_info["A/B分区"] = "是"
                        partition_info["分区数量"] = str(len(matching_partitions))

                        # 按名称排序显示
                        for i, (name, mount_point) in enumerate(sorted(matching_partitions.items())):
                            partition_info[f"分区{i+1}名称"] = name
                            partition_info[f"分区{i+1}挂载点"] = mount_point

                    # 获取分区大小（使用第一个找到的分区）
                    first_mount_point = list(matching_partitions.values())[0]
                    size_result = subprocess.run(
                        ["adb", "shell", f"blockdev --getsize64 {first_mount_point} 2>/dev/null || echo '0'"],
                        capture_output=True, text=True, timeout=5
                    )

                    if size_result.returncode == 0:
                        size_bytes = int(size_result.stdout.strip() or 0)
                        if size_bytes > 0:
                            size_mb = size_bytes / (1024 * 1024)
                            partition_info["分区大小"] = f"{size_mb:.1f} MB"
                        else:
                            partition_info["分区大小"] = "未知"
                else:
                    # 没有找到匹配的分区
                    return {}
            else:
                # 回退到原来的方法
                result = subprocess.run(
                    ["adb", "shell", f"ls /dev/block/by-name/{partition_name}* 2>/dev/null || echo 'not_found'"],
                    capture_output=True, text=True, timeout=5
                )

                if result.returncode == 0 and "not_found" not in result.stdout:
                    partitions = result.stdout.strip().split('\n')
                    partition_info["分区路径"] = partitions[0] if partitions else "未知"

                    # 获取分区大小
                    size_result = subprocess.run(
                        ["adb", "shell", f"blockdev --getsize64 {partitions[0]} 2>/dev/null || echo '0'"],
                        capture_output=True, text=True, timeout=5
                    )

                    if size_result.returncode == 0:
                        size_bytes = int(size_result.stdout.strip() or 0)
                        if size_bytes > 0:
                            size_mb = size_bytes / (1024 * 1024)
                            partition_info["分区大小"] = f"{size_mb:.1f} MB"
                        else:
                            partition_info["分区大小"] = "未知"

                    # 检查A/B分区
                    if len(partitions) > 1:
                        partition_info["A/B分区"] = "是"
                        partition_info["分区数量"] = str(len(partitions))
                    else:
                        partition_info["A/B分区"] = "否"

        except Exception as e:
            partition_info["错误"] = str(e)

        return partition_info

    def get_storage_info(self) -> dict:
        """获取存储信息"""
        storage_info = {}

        try:
            # 获取内部存储信息
            result = subprocess.run(
                ["adb", "shell", "df /data"],
                capture_output=True, text=True, timeout=5
            )

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) >= 2:
                    data_line = lines[1].split()
                    if len(data_line) >= 4:
                        total_kb = int(data_line[1])
                        used_kb = int(data_line[2])
                        available_kb = int(data_line[3])

                        storage_info["内部存储总容量"] = f"{total_kb / (1024 * 1024):.1f} GB"
                        storage_info["已使用空间"] = f"{used_kb / (1024 * 1024):.1f} GB"
                        storage_info["可用空间"] = f"{available_kb / (1024 * 1024):.1f} GB"
                        storage_info["使用率"] = f"{(used_kb / total_kb * 100):.1f}%"

            # 获取外部存储信息
            sdcard_result = subprocess.run(
                ["adb", "shell", "df /sdcard 2>/dev/null || echo 'no_sdcard'"],
                capture_output=True, text=True, timeout=5
            )

            if sdcard_result.returncode == 0 and "no_sdcard" not in sdcard_result.stdout:
                storage_info["外部存储"] = "已挂载"
            else:
                storage_info["外部存储"] = "未挂载"

        except Exception as e:
            storage_info["错误"] = str(e)

        return storage_info

    def get_mock_partition_mapping(self) -> dict:
        """获取模拟分区映射数据（用于测试）"""
        return {
            "boot_a": "/dev/block/mmcblk0p3",
            "boot_b": "/dev/block/mmcblk0p4",
            "dtbo_a": "/dev/block/mmcblk0p1",
            "dtbo_b": "/dev/block/mmcblk0p2",
            "system_a": "/dev/block/mmcblk0p5",
            "system_b": "/dev/block/mmcblk0p6",
            "misc": "/dev/block/mmcblk0p7",
            "metadata": "/dev/block/mmcblk0p8",
            "presistdata": "/dev/block/mmcblk0p9",
            "vendor_a": "/dev/block/mmcblk0p10",
            "vendor_b": "/dev/block/mmcblk0p11",
            "userdata": "/dev/block/mmcblk0p12",
            "hsaemisc": "/dev/block/mmcblk0p13",
            "fbmisc": "/dev/block/mmcblk0p14",
            "device": "/dev/block/mmcblk0p15",
            "vbmeta_a": "/dev/block/mmcblk0p16",
            "vbmeta_b": "/dev/block/mmcblk0p17",
            "bootloader": "/dev/block/platform/5b010000.usdhc/mmcblk0boot0"
        }

    def test_partition_mapping_display(self):
        """测试分区映射显示功能（使用模拟数据）"""
        self.add_log("🧪 开始测试分区映射显示功能", "step")

        # 使用模拟数据
        mock_mapping = self.get_mock_partition_mapping()

        self.add_log("📋 模拟分区挂载点映射:", "step")
        for partition_name, mount_point in sorted(mock_mapping.items()):
            self.add_log(f"  {partition_name} -> {mount_point}", "info")

        # 测试关键分区信息
        self.add_log("", "info")
        self.add_log("🔍 测试关键分区详细信息...", "info")
        key_partitions = ["boot", "system", "vendor", "vbmeta"]

        for partition in key_partitions:
            partition_info = self.get_partition_details(partition, mock_mapping)
            if partition_info:
                self.add_log(f"📦 {partition} 分区:", "step")
                for key, value in partition_info.items():
                    self.add_log(f"  {key}: {value}", "info")

        # 显示统计信息
        self.add_log("", "info")
        self.add_log("📊 分区表统计:", "step")
        total_partitions = len(mock_mapping)
        ab_partitions = len([name for name in mock_mapping.keys() if name.endswith('_a') or name.endswith('_b')])
        single_partitions = total_partitions - ab_partitions

        self.add_log(f"  总分区数量: {total_partitions}", "info")
        self.add_log(f"  A/B分区数量: {ab_partitions // 2 if ab_partitions > 0 else 0} 对", "info")
        self.add_log(f"  单一分区数量: {single_partitions}", "info")

        # 按类型分组显示
        boot_partitions = [name for name in mock_mapping.keys() if 'boot' in name.lower()]
        system_partitions = [name for name in mock_mapping.keys() if 'system' in name.lower()]
        vendor_partitions = [name for name in mock_mapping.keys() if 'vendor' in name.lower()]

        if boot_partitions:
            self.add_log(f"  启动相关分区: {', '.join(boot_partitions)}", "info")
        if system_partitions:
            self.add_log(f"  系统相关分区: {', '.join(system_partitions)}", "info")
        if vendor_partitions:
            self.add_log(f"  厂商相关分区: {', '.join(vendor_partitions)}", "info")

        self.add_log("", "info")
        self.add_log("✅ 分区映射显示功能测试完成！", "success")

    def show_license_info(self):
        """显示授权信息"""
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit, QGroupBox

        # 创建自定义授权信息对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("🔐 软件授权信息")
        dialog.setFixedSize(600, 500)
        dialog.setModal(True)

        # ACE优化：设置对话框图标
        self.set_dialog_icon(dialog)

        # 设置样式
        dialog.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 6px;
                margin-top: 1ex;
                padding-top: 12px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #333;
            }
            QLabel {
                padding: 4px 8px;
                background-color: #f8f9fa;
                border-radius: 4px;
                font-size: 13px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)

        layout = QVBoxLayout(dialog)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 获取授权信息
        license_info = self.license_manager.get_license_info()

        # 基本授权信息组
        basic_group = QGroupBox("📋 基本信息")
        basic_layout = QVBoxLayout(basic_group)

        machine_label = QLabel(f"🖥️ 机器码: {license_info['machine_code']}")
        machine_label.setStyleSheet("font-family: monospace; font-weight: bold; color: #0056b3;")
        basic_layout.addWidget(machine_label)

        status_label = QLabel(f"📊 授权状态: {license_info['license_status']}")
        status_color = "#4CAF50" if license_info['license_status'] == "已授权" else "#F44336"
        status_label.setStyleSheet(f"font-weight: bold; color: {status_color};")
        basic_layout.addWidget(status_label)

        if license_info['license_details']:
            details_label = QLabel(f"📝 详细信息: {license_info['license_details']}")
            details_label.setWordWrap(True)
            basic_layout.addWidget(details_label)

        layout.addWidget(basic_group)

        # 硬件信息组
        hardware_group = QGroupBox("🖥️ 硬件信息")
        hardware_layout = QVBoxLayout(hardware_group)

        for key, value in license_info['hardware_info'].items():
            hw_label = QLabel(f"{key.upper()}: {value}")
            hw_label.setStyleSheet("font-family: monospace; font-size: 12px;")
            hw_label.setWordWrap(True)
            hardware_layout.addWidget(hw_label)

        layout.addWidget(hardware_group)

        # 说明信息
        note_label = QLabel("💡 此软件采用一机一码授权机制，确保软件安全使用。")
        note_label.setStyleSheet("color: #666; font-style: italic; text-align: center; padding: 10px;")
        note_label.setWordWrap(True)
        layout.addWidget(note_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        close_btn = QPushButton("✅ 确定")
        close_btn.clicked.connect(dialog.accept)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        # 显示对话框
        dialog.exec()

    def browse_files_dir(self):
        """浏览文件目录 - ACE优化：使用内置目录，不允许更改"""
        # ACE优化：显示当前车型的文件信息
        QMessageBox.information(
            self,
            "文件目录信息",
            f"当前车型: {self.current_vehicle_model}\n"
            f"文件目录: {self.vehicle_models[self.current_vehicle_model]}\n\n"
            "必需文件：\n"
            "• boot.img (必需)\n"
            "• update-payload-key.pub.pem (必需)\n"
            "• otacerts.zip (必需)\n\n"
            "可选文件：\n"
            "• vbmeta.img (可选，部分设备可能不存在vbmeta分区)\n\n"
            "提示：可通过车型选择切换不同车型的文件配置。"
        )

        # 自动检查内置文件
        self.check_files()

    def check_files(self):
        """检查文件状态 - ACE优化：支持必需文件和可选文件检查"""
        self.add_log("🔍 开始检查文件...", "step")

        # 清空之前的文件状态显示
        for i in reversed(range(self.file_status_layout.count())):
            child = self.file_status_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # ACE优化：使用内置files目录，不依赖用户输入
        files_dir = self.files_dir
        self.add_log(f"📁 检查内置文件目录", "info")

        # 检查必需文件
        all_required_exist = True
        for file_name in self.required_files:
            file_path = files_dir / file_name
            file_widget = self._create_file_status_widget(file_name, file_path, is_required=True)
            self.file_status_layout.addWidget(file_widget)

            if not file_path.exists():
                all_required_exist = False
                self.add_log(f"❌ 缺少必需文件: {file_name}", "error")
            else:
                size = file_path.stat().st_size / (1024 * 1024)  # MB
                self.add_log(f"✅ 找到必需文件: {file_name} ({size:.1f}MB)", "success")

        # 检查可选文件
        optional_status = {}
        for file_name in self.optional_files:
            file_path = files_dir / file_name
            file_widget = self._create_file_status_widget(file_name, file_path, is_required=False)
            self.file_status_layout.addWidget(file_widget)

            if file_path.exists():
                size = file_path.stat().st_size / (1024 * 1024)  # MB
                self.add_log(f"✅ 找到可选文件: {file_name} ({size:.1f}MB)", "success")
                optional_status[file_name] = True
            else:
                self.add_log(f"⚠️ 可选文件不存在: {file_name} (部分设备可能不需要)", "warning")
                optional_status[file_name] = False

        # 保存可选文件状态供后续使用
        self.optional_file_status = optional_status

        # 更新状态
        if all_required_exist:
            self.add_log("🎉 所有必需文件检查通过！", "success")
            if any(optional_status.values()):
                self.add_log("📋 可选文件状态已记录，将根据设备兼容性使用", "info")
            self.status_label.setText("文件检查通过")
        else:
            self.add_log("⚠️ 文件检查失败，请确保所有必需文件存在", "error")
            self.status_label.setText("文件检查失败")

    def _create_file_status_widget(self, file_name: str, file_path: Path, is_required: bool) -> QWidget:
        """创建文件状态显示组件 - ACE优化：区分必需和可选文件，支持目录"""
        file_widget = QWidget()
        file_layout = QHBoxLayout(file_widget)
        file_layout.setContentsMargins(5, 2, 5, 2)

        if file_path.exists():
            # 检查是文件还是目录
            if file_path.is_dir():
                # 目录：显示文件数量
                file_count = len(list(file_path.iterdir()))
                status_text = f"✅ {file_name}"
                size_text = f"({file_count} 项)"
            else:
                # 文件：显示文件大小
                size = file_path.stat().st_size / (1024 * 1024)  # MB
                status_text = f"✅ {file_name}"
                size_text = f"({size:.1f}MB)"

            status_label = QLabel(status_text)
            status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")

            size_label = QLabel(size_text)
            size_label.setStyleSheet("color: #666; font-size: 11px;")
        else:
            if is_required:
                status_text = f"❌ {file_name}"
                size_text = "(缺失)"
                status_color = "#F44336"
            else:
                status_text = f"⚠️ {file_name}"
                size_text = "(可选)"
                status_color = "#FF9800"

            status_label = QLabel(status_text)
            status_label.setStyleSheet(f"color: {status_color}; font-weight: bold;")

            size_label = QLabel(size_text)
            size_label.setStyleSheet(f"color: {status_color}; font-size: 11px;")

        # 添加文件类型标识
        type_text = "(必需)" if is_required else "(可选)"
        type_label = QLabel(type_text)
        type_label.setStyleSheet("color: #888; font-size: 10px; font-style: italic;")

        file_layout.addWidget(status_label)
        file_layout.addWidget(type_label)
        file_layout.addStretch()
        file_layout.addWidget(size_label)

        return file_widget

    def on_vehicle_changed(self, vehicle_model: str):
        """车型切换处理 - ACE优化：车型切换功能"""
        if vehicle_model == self.current_vehicle_model:
            return  # 没有实际改变

        self.add_log(f"🚗 切换车型: {self.current_vehicle_model} → {vehicle_model}", "step")

        # 更新当前车型
        old_vehicle = self.current_vehicle_model
        self.current_vehicle_model = vehicle_model

        # 更新文件目录
        old_files_dir = self.files_dir
        self.files_dir = self.get_resource_path(self.vehicle_models[vehicle_model])

        # 更新状态显示
        self.vehicle_status_label.setText(f"✅ 已选择: {vehicle_model}")
        self.add_log(f"📁 文件目录已更新: {self.vehicle_models[vehicle_model]}", "info")

        # 更新文件目录显示
        self.files_dir_edit.setText(f"内置文件目录 ({vehicle_model})")

        # ACE优化：更新文件要求
        self.update_file_requirements()

        # 自动重新检查文件
        self.add_log("🔄 自动重新检查文件...", "info")
        self.check_files()

        # 记录切换完成
        self.add_log(f"✅ 车型切换完成: {vehicle_model}", "success")
        self.status_label.setText(f"已切换到 {vehicle_model}")

    def refresh_device(self):
        """手动刷新设备状态"""
        self.add_log("🔄 手动刷新设备状态...", "step")
        self.status_label.setText("刷新设备状态中...")

        # 强制更新设备信息
        if hasattr(self, 'device_thread'):
            device_info = self.device_thread.check_device_status()
            self.update_device_info(device_info)

        self.add_log("✅ 设备状态刷新完成", "success")
        self.status_label.setText("设备状态已刷新")

    def start_update(self):
        """开始OTA更新 - ACE实现：设备端一机一码授权验证"""
        if not self.device_info.connected or not self.device_info.authorized:
            QMessageBox.warning(self, "警告", "设备未连接或未授权，无法开始更新")
            return

        # ACE实现：设备端授权验证
        if not self.device_info.serial_number or self.device_info.serial_number == "未知":
            QMessageBox.warning(self, "设备授权验证", "无法获取设备序列号，无法进行授权验证")
            return

        # 验证设备授权
        is_device_authorized, auth_message = self.device_license_manager.verify_device_license(self.device_info.serial_number)

        if not is_device_authorized:
            # 显示设备未授权对话框
            self.show_device_unauthorized_dialog(auth_message)
            return

        # 设备已授权，记录授权信息
        self.add_log(f"✅ 设备授权验证通过: {self.device_info.serial_number}", "success")
        self.add_log(f"📝 授权详情: {auth_message}", "info")

        # 检查文件是否存在
        if not self.check_files_exist():
            QMessageBox.warning(self, "警告", "请先检查并确保所有必需文件存在")
            return

        # ACE优化：根据车型显示不同的确认对话框
        if self.current_vehicle_model == "奔腾D357_4":
            operation_type = "APK应用更新"
            operation_desc = "此操作将更新系统应用文件"
        else:
            operation_type = "OTA秘钥更新"
            operation_desc = "此操作将修改设备系统分区"

        reply = QMessageBox.question(
            self,
            "确认更新",
            f"确定要开始{operation_type}吗？\n\n"
            f"车型: {self.current_vehicle_model}\n"
            f"操作: {operation_desc}\n\n"
            "请确保：\n"
            "1. 设备电量充足\n"
            "2. 已备份重要数据\n"
            "3. 了解操作风险\n"
            "4. 设备已获取root权限",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.add_log(f"🚀 开始{operation_type}流程...", "step")
            self.status_label.setText(f"正在执行{operation_type}...")

            # 禁用更新按钮防止重复点击
            self.update_btn.setEnabled(False)
            self.update_btn.setText("🔄 更新中...")

            # ACE优化：启动对应车型的更新线程
            self.ota_thread = OTAUpdateThread(self.files_dir, self.current_vehicle_model)
            self.ota_thread.log_message.connect(self.add_log)
            self.ota_thread.update_finished.connect(self.on_update_finished)
            self.ota_thread.start()

    def on_update_finished(self, success):
        """OTA更新完成回调"""
        if success:
            self.add_log("✅ O秘钥更新流程完成！", "success")
            self.status_label.setText("更新完成")
            QMessageBox.information(
                self,
                "更新完成",
                "秘钥更新已完成！\n设备正在重启，请等待启动完成。"
            )
        else:
            self.add_log("❌ 秘钥更新失败", "error")
            self.status_label.setText("更新失败")
            QMessageBox.critical(
                self,
                "更新失败",
                "秘钥更新失败，请检查日志了解详细信息。"
            )

        # 恢复更新按钮
        self.update_btn.setEnabled(True)
        self.update_btn.setText("🚀 开始更新")

    def check_files_exist(self):
        """检查所需文件是否存在 - ACE优化：只检查必需文件，可选文件不影响结果"""
        # 只检查必需文件
        for file_name in self.required_files:
            file_path = self.files_dir / file_name
            if not file_path.exists():
                return False
        return True

    # def test_display(self):
    #     """测试显示功能 - 已被get_partition_info替代"""
    #     # 此方法已被新的获取分区信息功能替代
    #     pass

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log("🗑️ 日志已清空", "info")
        self.add_log("🚀 Android 秘钥更新工具就绪", "success")

    def save_log(self):
        """保存日志"""
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "保存日志",
            f"ota_log_{time.strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    # 获取纯文本内容（去除HTML格式）
                    plain_text = self.log_text.toPlainText()
                    f.write(plain_text)

                QMessageBox.information(self, "成功", f"日志已保存到: {filename}")
                self.add_log(f"💾 日志已保存到: {filename}", "success")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败: {e}")
                self.add_log(f"❌ 日志保存失败: {e}", "error")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于",
            """
            <h3>Android 车机通用刷机工具</h3>
            <p><b>版本:</b>  跨平台现代化版本</p>
            <p><b>作者:</b> UltraMan</p>
            <p><b>描述:</b> Android 车机通用刷机工具</p>
            <br>
            <p><b>特性:</b></p>
            <ul>
            <li>✅ 跨平台兼容性</li>
            <li>✅ 现代化界面设计</li>
            <li>✅ 实时设备监控</li>
            <li>✅ 详细日志记录</li>
            </ul>
            """
        )

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止设备监控线程
        if hasattr(self, 'device_thread'):
            self.device_thread.stop()
            self.device_thread.wait(3000)  # 等待最多3秒

        event.accept()


def check_license():
    """检查授权"""
    try:
        license_manager = LicenseManager()
        is_valid, _ = license_manager.verify_license()
        return is_valid
    except Exception:
        return False


def main():
    """主函数 - ACE优化：修复变量作用域问题"""
    app = None
    try:
        # 设置GUI模式标志，允许必要的输出
        sys._gui_mode = True

        # 创建应用
        app = QApplication(sys.argv)

        # 设置应用信息
        app.setApplicationName("Android 秘钥更新工具")
        app.setApplicationVersion("PySide6-1.0")
        app.setOrganizationName("OTA Tools")

        # 检查授权 - ACE优化：改进错误处理
        try:
            if not check_license():
                # 显示授权对话框
                if not show_license_dialog():
                    # 用户取消授权，正常退出
                    return
        except Exception as e:
            # 授权检查异常，显示错误并继续（调试模式）
            QMessageBox.critical(None, "授权检查错误", f"授权检查失败: {str(e)}\n\n程序将继续运行以便调试。")

        # 创建主窗口
        window = ModernOTAGUI()
        window.show()

        # 运行应用
        sys.exit(app.exec())

    except Exception as e:
        # 捕获所有异常，显示错误信息
        try:
            # 检查是否已有QApplication实例
            current_app = QApplication.instance()
            if not current_app:
                # 如果没有，创建一个新的
                current_app = QApplication(sys.argv)
            QMessageBox.critical(None, "程序启动错误", f"程序启动失败:\n\n{str(e)}\n\n请检查依赖是否完整。")
        except:
            # 如果连错误对话框都无法显示，写入文件
            try:
                with open("error_log.txt", "w", encoding="utf-8") as f:
                    f.write(f"程序启动错误: {str(e)}\n")
                    f.write(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    import traceback
                    f.write(f"详细错误信息:\n{traceback.format_exc()}\n")
            except:
                pass
        sys.exit(1)


if __name__ == "__main__":
    main()
