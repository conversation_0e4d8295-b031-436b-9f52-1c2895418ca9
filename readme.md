# payload.bin privkey
openssl pkcs8 -inform DER -nocrypt -in releasekey.pk8 -out releasekey_pri.pem

# ota pubkey
openssl x509 -in releasekey.x509.pem -pubkey -noout > releasekey.pem

cp releasekey.pem update-payload-key.pub.pem

zip otacerts.zip releasekey.x509.pem 

cp update-payload-key.pub.pem /Users/<USER>/reverse/TIK_SOURCE/TIK/d357_3/system/system/etc/update_engine/update-payload-key.pub.pem

cp otacerts.zip /Users/<USER>/reverse/TIK_SOURCE/TIK/d357_3/system/system/etc/security/otacerts.zip



self/platform.jks
对应的plat_mac_permissions.xml中platform签名信息
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


# 用命令更新payload.bin
payload_properties=`cat /data/for_ota/payload_properties.txt`
update_engine_client --payload=file:///data/for_ota/payload.bin --update --headers="${payload_properties}"

更新完之后，查看app及相关的文件已经更新了，但是因为缓存原因，系统还记录是旧的签名信息，需要清理缓存。

# 清理缓存--待验证
adb shell rm -f /data/system/packages.xml
adb shell rm -f /data/system/packages.xml.backup
adb shell rm -f /data/system/packages.list
adb shell rm -f /data/system/packages.list.backup

adb shell rm -rf /data/dalvik-cache/


固件里面的SystemUI二次打包出问题， 需要用apktool if 安装framework-res.apk，然后再重新反编译/回编译二次打包，一定要安装后再次反编译，不能用未安装前反编译的进行回编译。


# 更新系统ota签名

adb root
adb is already running as root

adb remount
dm_verity is enabled on the vendor partition. Use "adb disable-verity" to disable verity.
If you do not, remount may succeed, however, you will still not be able to write to these volumes. 
adb disable-verity 
Successfully disabled verity
Now reboot your device for settings to take effect

adb reboot

adb root

adb remount
remount succeeded

adb push boot.img /data/local/tmp

adb push vbmeta.img /data/local/tmp


adb shell getprop ro.boot.slot_suffix
_a

adb shell ls -l /dev/block/by-name
total 0
lrwxrwxrwx 1 root root 20 1970-01-01 03:00 boot_a -> /dev/block/mmcblk0p3
lrwxrwxrwx 1 root root 20 1970-01-01 03:00 boot_b -> /dev/block/mmcblk0p4
lrwxrwxrwx 1 root root 47 1970-01-01 03:00 bootloader -> /dev/block/platform/5b010000.usdhc/mmcblk0boot0
lrwxrwxrwx 1 root root 21 1970-01-01 03:00 device -> /dev/block/mmcblk0p15
lrwxrwxrwx 1 root root 20 1970-01-01 03:00 dtbo_a -> /dev/block/mmcblk0p1
lrwxrwxrwx 1 root root 20 1970-01-01 03:00 dtbo_b -> /dev/block/mmcblk0p2
lrwxrwxrwx 1 root root 21 1970-01-01 03:00 fbmisc -> /dev/block/mmcblk0p14
lrwxrwxrwx 1 root root 21 1970-01-01 03:00 hsaemisc -> /dev/block/mmcblk0p13
lrwxrwxrwx 1 root root 20 1970-01-01 03:00 metadata -> /dev/block/mmcblk0p8
lrwxrwxrwx 1 root root 20 1970-01-01 03:00 misc -> /dev/block/mmcblk0p7
lrwxrwxrwx 1 root root 20 1970-01-01 03:00 presistdata -> /dev/block/mmcblk0p9
lrwxrwxrwx 1 root root 20 1970-01-01 03:00 system_a -> /dev/block/mmcblk0p5
lrwxrwxrwx 1 root root 20 1970-01-01 03:00 system_b -> /dev/block/mmcblk0p6
lrwxrwxrwx 1 root root 21 1970-01-01 03:00 userdata -> /dev/block/mmcblk0p12
lrwxrwxrwx 1 root root 21 1970-01-01 03:00 vbmeta_a -> /dev/block/mmcblk0p16
lrwxrwxrwx 1 root root 21 1970-01-01 03:00 vbmeta_b -> /dev/block/mmcblk0p17
lrwxrwxrwx 1 root root 21 1970-01-01 03:00 vendor_a -> /dev/block/mmcblk0p10
lrwxrwxrwx 1 root root 21 1970-01-01 03:00 vendor_b -> /dev/block/mmcblk0p11

adb shell dd if=/data/local/tmp/boot.img of=/dev/block/mmcblk0p3
adb shell dd if=/data/local/tmp/vbmeta.img of=/dev/block/mmcblk0p16

adb push update-payload-key.pub.pem /etc/update_engine/update-payload-key.pub.pem
adb push update-payload-key.pub.pem /system/etc/update_engine/update-payload-key.pub.pem
adb push otacerts.zip /etc/security/otacerts.zip
adb push otacerts.zip /system/etc/security/otacerts.zip

adb reboot
