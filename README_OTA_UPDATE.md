# Android OTA签名自动更新工具

这个工具包提供了多种方式来自动化执行Android设备的OTA签名更新流程，包括命令行版本和图形界面版本。

## 功能特性

- 自动检测和禁用dm-verity
- 智能分区路径检测
- 自动推送镜像文件
- 更新OTA签名证书
- 完整的错误处理和状态反馈
- 多种界面选择（命令行/基础GUI/高级GUI）
- 实时设备状态监控
- 详细的执行日志

## 前置要求

1. **ADB工具**: 确保系统中已安装Android Debug Bridge (ADB)
2. **Python 3.6+**: 脚本需要Python 3.6或更高版本
3. **设备权限**: Android设备需要开启USB调试，并且可以获取root权限
4. **必需文件**: 在`files`文件夹中准备以下文件：
   - `boot.img` - 启动镜像
   - `vbmeta.img` - 验证启动元数据镜像
   - `update-payload-key.pub.pem` - OTA更新公钥
   - `otacerts.zip` - OTA证书包

## 工具版本

### 1. 启动器（推荐）
```bash
python3 run_gui.py
```
提供统一的启动界面，可以选择不同版本的工具。

### 2. 命令行版本
```bash
python3 update_ota_signature.py
```
传统的命令行界面，适合脚本自动化和高级用户。

### 3. 基础GUI版本
```bash
python3 ota_gui.py
```
简单的图形界面版本，提供基本的GUI操作。

### 4. 高级GUI版本（推荐）
```bash
python3 ota_gui_advanced.py
```
功能丰富的图形界面版本，包含：
- 实时设备状态监控
- 文件状态检查
- 详细的执行日志
- 菜单栏和工具栏
- 设备信息查看

## 使用方法

### 1. 准备文件
确保`files`文件夹中包含所有必需的文件：
```
files/
├── boot.img
├── vbmeta.img
├── update-payload-key.pub.pem
└── otacerts.zip
```

### 2. 连接设备
- 通过USB连接Android设备
- 确保设备已开启USB调试
- 在设备上授权计算机的ADB访问

### 3. 运行工具

#### 使用启动器（推荐）
1. 运行 `python3 run_gui.py`
2. 选择合适的版本启动

#### 使用高级GUI版本
1. 运行 `python3 ota_gui_advanced.py`
2. 点击"浏览"选择文件目录（如果不是默认的files目录）
3. 点击"检查文件"确认所有必需文件存在
4. 确认设备状态显示为绿色（已连接、已授权、已获取root权限）
5. 点击"开始更新"执行OTA签名更新
6. 在确认对话框中点击"是"开始更新

#### 使用命令行版本
1. 运行 `python3 update_ota_signature.py`
2. 输入`y`确认操作

## 执行流程

脚本将按以下顺序执行操作：

1. **文件检查**: 验证所有必需文件是否存在
2. **设备连接**: 等待设备连接并获取root权限
3. **Verity处理**: 检查并禁用dm-verity（如果需要）
4. **文件推送**: 将镜像文件推送到设备临时目录
5. **分区检测**: 自动检测当前活动槽位和分区路径
6. **镜像写入**: 将boot和vbmeta镜像写入对应分区
7. **证书更新**: 推送OTA签名证书到系统目录
8. **设备重启**: 重启设备以应用更改

## 安全注意事项

⚠️ **警告**: 此操作会修改系统关键分区，可能导致设备无法启动。请确保：

- 了解操作风险并有恢复方案
- 备份原始镜像文件
- 确认镜像文件与设备兼容
- 在测试设备上先行验证

## 故障排除

### 常见问题

1. **设备未找到**
   - 检查USB连接
   - 确认USB调试已开启
   - 尝试重新授权ADB访问

2. **权限不足**
   - 确保设备已root
   - 检查ADB是否以root权限运行

3. **分区写入失败**
   - 确认dm-verity已正确禁用
   - 检查分区路径是否正确
   - 验证镜像文件完整性

4. **文件推送失败**
   - 检查设备存储空间
   - 确认文件路径正确
   - 验证文件权限

### 日志分析

脚本会输出详细的执行日志，包括：
- 每个步骤的执行状态
- ADB命令的输出结果
- 错误信息和建议

## 技术细节

### 分区检测逻辑
脚本会自动：
- 检测当前活动槽位 (A/B分区)
- 解析`/dev/block/by-name`中的分区映射
- 选择正确的boot和vbmeta分区

### 错误处理
- 每个关键步骤都有错误检查
- 超时保护防止命令挂起
- 详细的错误信息便于调试

## 许可证

此脚本仅供学习和研究使用。使用者需自行承担使用风险。

## 支持

如遇问题，请检查：
1. 设备兼容性
2. 文件完整性
3. ADB连接状态
4. 系统权限设置
