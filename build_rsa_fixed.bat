@echo off
REM ========================================
REM Android OTA Tool - RSA Security Build Script
REM ACE implementation - Fixed encoding + RSA Security
REM Features: Performance optimized + No console + No admin + RSA digital signature
REM ========================================

setlocal enabledelayedexpansion

REM Set encoding to handle Chinese characters properly
chcp 936 >nul 2>&1
if errorlevel 1 chcp 65001 >nul 2>&1

echo.
echo ========================================
echo Android OTA Tool - RSA Security Build
echo ========================================
echo.

REM Quick checks
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found
    pause
    exit /b 1
)

if not exist "ota_gui_pyside6.py" (
    echo [ERROR] Main file not found
    pause
    exit /b 1
)

echo [INFO] Building OPTIMIZED GUI version (ACE RSA Security Mode)
echo [INFO] Features: Fast startup + Working GUI + No admin + RSA digital signature
echo [INFO] ACE Analysis: Performance optimized with RSA security and module exclusions
echo.

REM Install dependencies (ACE RSA Security: cryptography required)
echo [INFO] Installing dependencies with RSA security support...
pip install nuitka PySide6 cryptography --upgrade --quiet

REM Verify cryptography installation for RSA support
echo [INFO] Verifying RSA security dependencies...
python -c "from cryptography.hazmat.primitives.asymmetric import rsa; print('RSA cryptography support verified')" 2>nul
if errorlevel 1 (
    echo [WARNING] Cryptography installation may have issues
    echo [INFO] RSA verification will fallback to HMAC if needed
)

REM Create output directory
if not exist "dist" mkdir dist

REM Set parameters
set "MAIN_FILE=ota_gui_pyside6.py"
set "OUTPUT_NAME=Android_OTA_Tool_RSA"
set "OUTPUT_DIR=dist"

echo [INFO] Starting RSA-secured GUI build...
echo [INFO] This will create a working GUI without console windows
echo.

REM Optimized GUI Build - ACE performance optimization with RSA security
REM ACE优化：不打包files和licenses文件夹，使用外部文件
REM ACE优化：添加应用程序图标
echo [INFO] Checking for application icon...
if exist "icon.ico" (
    echo [INFO] Found icon.ico - will be used as application icon
) else (
    echo [WARNING] icon.ico not found - executable will use default icon
)

python -m nuitka ^
    --standalone ^
    --onefile ^
    --windows-console-mode=disable ^
    --output-filename=%OUTPUT_NAME%.exe ^
    --output-dir=%OUTPUT_DIR% ^
    --plugin-enable=pyside6 ^
    --windows-icon-from-ico=icon.ico ^
    --assume-yes-for-downloads ^
    --show-progress ^
    --remove-output ^
    --company-name="OTA Tools" ^
    --product-name="Android OTA Tool RSA" ^
    --file-version=******* ^
    --file-description="Android OTA Tool - RSA Security" ^
    --copyright="Copyright (C) 2024" ^
    --follow-imports ^
    --nofollow-import-to=tkinter ^
    --nofollow-import-to=matplotlib ^
    --nofollow-import-to=numpy ^
    --nofollow-import-to=scipy ^
    --nofollow-import-to=pandas ^
    --python-flag=no_site ^
    --python-flag=no_warnings ^
    %MAIN_FILE%

if errorlevel 1 (
    echo.
    echo [WARNING] Full build failed, trying minimal RSA build...
    
    REM Fast startup fallback - ACE optimized for speed with RSA
    python -m nuitka ^
        --onefile ^
        --windows-console-mode=disable ^
        --plugin-enable=pyside6 ^
        --windows-icon-from-ico=icon.ico ^
        --output-filename=%OUTPUT_NAME%_fast.exe ^
        --output-dir=%OUTPUT_DIR% ^
        --assume-yes-for-downloads ^
        --remove-output ^
        --nofollow-import-to=tkinter ^
        --python-flag=no_warnings ^
        %MAIN_FILE%
    
    if errorlevel 1 (
        echo [ERROR] RSA GUI build failed
        pause
        exit /b 1
    ) else (
        set "OUTPUT_NAME=%OUTPUT_NAME%_fast"
        echo [SUCCESS] Fast RSA build completed
    )
) else (
    echo [SUCCESS] Full RSA build completed
)

echo.
echo ========================================
echo RSA-Secured GUI Build Completed!
echo ========================================
echo.

REM Verify output
if exist "%OUTPUT_DIR%\%OUTPUT_NAME%.exe" (
    echo [SUCCESS] RSA-secured GUI executable: %OUTPUT_DIR%\%OUTPUT_NAME%.exe

    REM Copy license.dat to exe directory (ACE requirement: external license file)
    if exist "license.dat" (
        copy "license.dat" "%OUTPUT_DIR%\" >nul 2>&1
        echo [INFO] License file copied to exe directory
    ) else (
        echo [WARNING] license.dat not found - please ensure it exists for authorization
    )

    REM ACE优化：复制files文件夹到exe同级目录（外部文件）
    echo [INFO] Copying external directories to exe directory...
    if exist "files" (
        if not exist "%OUTPUT_DIR%\files" mkdir "%OUTPUT_DIR%\files"
        xcopy "files\*" "%OUTPUT_DIR%\files\" /E /I /Y >nul 2>&1
        if errorlevel 1 (
            echo [WARNING] Failed to copy files directory
        ) else (
            echo [INFO] Files directory copied to exe directory
        )
    ) else (
        echo [WARNING] files directory not found - OTA resources may be missing
    )

    REM ACE优化：复制licenses文件夹到exe同级目录（设备授权文件）
    if exist "licenses" (
        if not exist "%OUTPUT_DIR%\licenses" mkdir "%OUTPUT_DIR%\licenses"
        xcopy "licenses\*" "%OUTPUT_DIR%\licenses\" /E /I /Y >nul 2>&1
        if errorlevel 1 (
            echo [WARNING] Failed to copy licenses directory
        ) else (
            echo [INFO] Licenses directory copied to exe directory
        )
    ) else (
        echo [INFO] licenses directory not found - will be created when needed
    )

    REM Get file size
    for %%A in ("%OUTPUT_DIR%\%OUTPUT_NAME%.exe") do (
        set "file_size=%%~zA"
        set /a "size_mb=!file_size!/1024/1024"
        echo [INFO] File size: !size_mb! MB
    )
    
    echo.
    echo [INFO] OPTIMIZED GUI Features (ACE RSA Security):
    echo   - Fast startup (module exclusions + compatible flags)
    echo   - RSA digital signature security (cryptography support)
    echo   - Custom application icon (icon.ico)
    echo   - Reduced file size (excluded tkinter, matplotlib, numpy, etc.)
    echo   - NO admin privileges required
    echo   - Functional GUI interface
    echo   - RSA-secured authorization dialog (external license.dat)
    echo   - Working device monitoring
    echo   - Functional OTA updates
    echo   - Compatible with Nuitka 2.7.10+
    echo   - No console windows
    echo   - External file structure (files/ and licenses/ directories)
    echo   - External license file support (RSA-signed)
    echo.
    
    REM Test run option
    set /p "test_run=Test the RSA GUI executable? (y/n): "
    if /i "!test_run!"=="y" (
        echo [INFO] Launching RSA GUI application...
        echo [INFO] Should start without console and work normally
        start "" "%OUTPUT_DIR%\%OUTPUT_NAME%.exe"
        echo [INFO] Check if GUI appears and functions properly
    )
) else (
    echo [ERROR] RSA GUI executable not found
    pause
    exit /b 1
)

REM Cleanup
if exist "build" rmdir /s /q "build" >nul 2>&1

echo.
echo [COMPLETE] RSA-secured GUI build process finished
echo [NOTE] This executable provides:
echo   - Fast startup and optimized performance
echo   - RSA digital signature security (enterprise-grade)
echo   - Custom application icon (professional appearance)
echo   - No console windows
echo   - No admin requirement
echo   - Full GUI functionality
echo   - External file structure for easy management
echo.
echo [IMPORTANT] External file structure:
echo   - license.dat: PC authorization file (same directory as exe)
echo   - files/: OTA resource files (boot.img, vbmeta.img, etc.)
echo   - licenses/: Device authorization files ({serial}.dat format)
echo   - All files must be in the same directory as the exe
echo.
echo [IMPORTANT] RSA License file requirements:
echo   - PC license: license.dat (RSA-signed using license_generator_rsa.py)
echo   - Device licenses: {serial}.dat (RSA-signed using device_license_generator.py)
echo   - RSA public key is embedded in exe for verification
echo   - RSA private key is kept secure on license server only
echo.
echo [RSA SECURITY] Enhanced authorization features:
echo   - RSA-2048 digital signature verification
echo   - Asymmetric cryptography (private/public key separation)
echo   - Cryptographic integrity protection
echo   - Non-repudiation (only license server can create valid licenses)
echo   - Forward security (compromised client cannot generate licenses)
echo.
echo [TOOLS] RSA license management:
echo   - Use license_generator_rsa.py to generate RSA-signed licenses
echo   - Private key (rsa_private_key.pem) must be kept secure
echo   - Public key is embedded in the application for verification
echo.

pause
