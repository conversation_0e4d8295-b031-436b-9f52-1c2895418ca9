#!/usr/bin/env python3
"""
RSA授权码生成器 - ACE安全实现
此工具用于生成RSA签名的授权码，私钥不会打包进exe
"""

import json
import base64
from datetime import datetime, timedelta
from pathlib import Path

try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.backends import default_backend
    RSA_AVAILABLE = True
except ImportError:
    print("❌ 错误：需要安装cryptography库")
    print("请运行：pip install cryptography")
    RSA_AVAILABLE = False
    exit(1)


class RSALicenseGenerator:
    """RSA授权码生成器"""
    
    def __init__(self):
        self.private_key_file = Path("rsa_private_key.pem")
        self.public_key_file = Path("rsa_public_key.pem")
    
    def generate_rsa_keys(self):
        """生成RSA密钥对"""
        print("🔑 生成RSA密钥对...")
        
        # 生成私钥
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=default_backend()
        )
        
        # 获取公钥
        public_key = private_key.public_key()
        
        # 保存私钥
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        with open(self.private_key_file, 'wb') as f:
            f.write(private_pem)
        
        # 保存公钥
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        with open(self.public_key_file, 'wb') as f:
            f.write(public_pem)
        
        print(f"✅ 私钥已保存到: {self.private_key_file}")
        print(f"✅ 公钥已保存到: {self.public_key_file}")
        print()
        print("⚠️  重要提醒:")
        print("   1. 私钥文件请妥善保管，不要泄露")
        print("   2. 私钥不能打包进exe文件")
        print("   3. 公钥需要更新到license_manager.py中")
        print()
        
        # 显示公钥内容，用于更新到程序中
        print("📋 请将以下公钥内容更新到license_manager.py中的rsa_public_key_pem:")
        print("-" * 60)
        print(public_pem.decode())
        print("-" * 60)
    
    def load_private_key(self):
        """加载私钥"""
        if not self.private_key_file.exists():
            raise FileNotFoundError(f"私钥文件不存在: {self.private_key_file}")
        
        with open(self.private_key_file, 'rb') as f:
            private_key = serialization.load_pem_private_key(
                f.read(),
                password=None,
                backend=default_backend()
            )
        return private_key
    
    def sign_data(self, data: str) -> str:
        """使用RSA私钥签名数据"""
        private_key = self.load_private_key()
        
        signature = private_key.sign(
            data.encode('utf-8'),
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        return base64.b64encode(signature).decode()
    
    def generate_license_key(self, machine_code: str, days: int = 365) -> str:
        """生成RSA签名的授权码"""
        # 计算过期时间
        expire_date = datetime.now() + timedelta(days=days)
        expire_timestamp = int(expire_date.timestamp())
        
        # 创建授权数据
        license_data = {
            'machine_code': machine_code,
            'expire_timestamp': expire_timestamp,
            'version': '1.0',
            'features': ['ota_update', 'partition_info', 'device_monitor']
        }
        
        # 序列化数据
        data_str = json.dumps(license_data, sort_keys=True)
        
        # 使用RSA私钥签名
        signature = self.sign_data(data_str)
        
        # 组合数据和签名
        license_content = {
            'data': license_data,
            'signature': signature
        }
        
        # Base64编码
        license_key = base64.b64encode(
            json.dumps(license_content).encode()
        ).decode()
        
        return license_key


def main():
    """主函数"""
    print("🔐 RSA授权码生成器")
    print("=" * 50)
    
    generator = RSALicenseGenerator()
    
    # 检查是否需要生成密钥
    if not generator.private_key_file.exists() or not generator.public_key_file.exists():
        print("🔑 未找到RSA密钥，开始生成...")
        generator.generate_rsa_keys()
        print()
        input("请按回车键继续...")
        print()
    
    # 生成授权码
    while True:
        print("📝 授权码生成")
        print("-" * 30)
        
        # 获取机器码
        machine_code = input("请输入机器码: ").strip().upper()
        if not machine_code:
            print("❌ 机器码不能为空")
            continue
        
        # 获取有效期
        try:
            days = int(input("请输入有效期（天数，默认365）: ") or "365")
        except ValueError:
            print("❌ 有效期必须是数字")
            continue
        
        try:
            # 生成授权码
            license_key = generator.generate_license_key(machine_code, days)
            
            # 计算过期时间
            expire_date = datetime.now() + timedelta(days=days)
            
            print()
            print("✅ 授权码生成成功!")
            print(f"机器码: {machine_code}")
            print(f"有效期: {days} 天")
            print(f"过期时间: {expire_date.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            print("授权码:")
            print("-" * 60)
            print(license_key)
            print("-" * 60)
            print()
            
            # 保存到文件
            filename = f"license_{machine_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"机器码: {machine_code}\n")
                f.write(f"有效期: {days} 天\n")
                f.write(f"过期时间: {expire_date.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("\n授权码:\n")
                f.write(license_key)
            
            print(f"📄 授权码已保存到: {filename}")
            
        except Exception as e:
            print(f"❌ 生成授权码失败: {str(e)}")
        
        print()
        if input("是否继续生成授权码？(y/n): ").lower() != 'y':
            break
    
    print("👋 再见!")


if __name__ == "__main__":
    if not RSA_AVAILABLE:
        exit(1)
    main()
