# Android OTA Tool - Nuitka Build Guide

## 📋 Overview

This document explains how to use <PERSON><PERSON>ka to package the Android OTA Signature Update Tool into a single Windows executable file.

## 🛠️ ACE-Implemented Build Scripts

### Script Files
- `build_exe_with_nuitka.bat` - Main build script (English interface)
- `build_exe_enhanced.bat` - Enhanced build script with multi-encoding support
- `build_simple.bat` - Simplified build script
- `requirements.txt` - Python dependencies list

### Script Features
1. **Automatic Environment Check** - Checks Python, pip, nuitka and other necessary tools
2. **Dependency Auto-Installation** - Automatically installs missing dependency packages
3. **Smart Error Handling** - Provides fallback build options
4. **Complete Resource Inclusion** - Automatically includes all necessary files and resources
5. **Version Info Embedding** - Adds version and company information to exe file
6. **Multi-Encoding Support** - Optimized for both Chinese and English Windows systems

## 🚀 Usage Instructions

### 1. Environment Preparation
Ensure the following are installed on Windows system:
- Python 3.8 or higher
- pip package manager
- Stable internet connection (for downloading dependencies)

### 2. Choose and Run Build Script

#### Option A: Enhanced Script (Recommended)
```batch
# Run in project root directory
build_exe_enhanced.bat
```
- Best encoding compatibility
- Multiple fallback build options
- Detailed progress reporting

#### Option B: Main Script
```batch
# Run in project root directory
build_exe_with_nuitka.bat
```
- Full-featured build with all optimizations
- English interface to avoid encoding issues

#### Option C: Simple Script
```batch
# Run in project root directory
build_simple.bat
```
- Quick and minimal build
- For testing purposes

### 3. Build Process
The script will automatically execute the following steps:
1. Check Python and pip environment
2. Install nuitka and PySide6 (if not installed)
3. Verify all source files exist
4. Create requirements.txt (if not exists)
5. Install all dependency packages
6. Use nuitka for packaging
7. If failed, try simplified mode repackaging
8. Generate final exe file

## 📁 输出结果

### 成功打包后将生成：
- `dist/Android_OTA_Tool.exe` - 主要可执行文件
- 或 `dist/Android_OTA_Tool_simple.exe` - 简化模式文件

### 文件特性：
- **单文件模式** - 所有依赖打包在一个exe中
- **无控制台窗口** - GUI模式运行
- **管理员权限** - 自动请求UAC提升
- **完整资源** - 包含files文件夹和授权文件

## ⚙️ 打包参数说明

### 主要参数：
- `--standalone` - 独立模式，包含所有依赖
- `--onefile` - 单文件模式
- `--windows-console-mode=disable` - 禁用控制台窗口
- `--plugin-enable=pyside6` - 启用PySide6插件
- `--include-data-dir=files=files` - 包含资源文件夹
- `--windows-uac-admin` - 请求管理员权限

### 包含的模块：
- `license_manager` - 授权管理
- `license_dialog` - 授权对话框
- `update_ota_signature` - OTA更新核心
- `check_prerequisites` - 前置条件检查

## 🔧 故障排除

### 常见问题：

1. **Python版本过低**
   - 解决：升级到Python 3.8或更高版本

2. **nuitka安装失败**
   - 解决：手动安装 `pip install nuitka`

3. **PySide6安装失败**
   - 解决：手动安装 `pip install PySide6`

4. **打包过程中断**
   - 解决：检查磁盘空间，关闭杀毒软件

5. **exe文件无法运行**
   - 解决：安装Visual C++ Redistributable

### 调试模式：
如果需要查看详细错误信息，可以修改脚本中的参数：
- 移除 `--windows-console-mode=disable`
- 添加 `--debug` 参数

## 📦 部署说明

### 在目标机器上运行：
1. 确保安装了Visual C++ Redistributable
2. 将exe文件复制到目标机器
3. 确保ADB工具可用（在PATH中或同目录）
4. 以管理员权限运行exe文件

### 系统要求：
- Windows 7 或更高版本
- 至少2GB可用内存
- 100MB可用磁盘空间
- USB端口（用于连接Android设备）

## 🎯 优化建议

### 减小文件大小：
1. 使用 `--follow-imports` 仅包含必要模块
2. 移除不必要的 `--include-package` 参数
3. 使用 `--plugin-no-detection` 禁用自动检测

### 提高启动速度：
1. 使用 `--standalone` 而非 `--onefile`
2. 预编译Python字节码
3. 优化资源文件大小

## 📝 版本历史

- v1.0 - 初始版本，基本打包功能
- v1.1 - 添加错误处理和备用方案
- v1.2 - 集成ACE优化，完善资源包含

## 🤝 技术支持

如果在打包过程中遇到问题，请检查：
1. Python和pip版本
2. 网络连接状态
3. 磁盘空间是否充足
4. 杀毒软件是否干扰
5. 源文件是否完整
