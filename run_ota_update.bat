@echo off
chcp 65001 >nul
title Android OTA签名更新工具

echo ========================================
echo Android OTA签名更新工具
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.6或更高版本
    pause
    exit /b 1
)

:: 检查ADB是否安装
adb version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到ADB工具，请先安装Android SDK Platform Tools
    pause
    exit /b 1
)

echo 1. 运行前置条件检查
echo 2. 执行OTA签名更新
echo 3. 退出
echo.
set /p choice="请选择操作 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 正在运行前置条件检查...
    python check_prerequisites.py
    pause
    goto :eof
)

if "%choice%"=="2" (
    echo.
    echo 正在执行OTA签名更新...
    python update_ota_signature.py
    pause
    goto :eof
)

if "%choice%"=="3" (
    exit /b 0
)

echo 无效选择，请重新运行脚本
pause
